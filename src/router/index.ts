import { createRouter, createWebHistory } from '@ionic/vue-router';
import { RouteRecordRaw } from 'vue-router';
import WebContainerPage from '../views/WebContainerPage.vue'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    redirect: '/container'
  },
  {
    path: '/container',
    name: 'WebContainer',
    component: WebContainerPage
  },
  {
    path: '/test',
    name: 'Test',
    component: () => import('@/views/TestPage.vue')
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

export default router
