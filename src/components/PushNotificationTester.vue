<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-title>Push Notification Tester</ion-title>
      </ion-toolbar>
    </ion-header>
    
    <ion-content class="ion-padding">
      <ion-card>
        <ion-card-header>
          <ion-card-title>Registration Testing</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <ion-button expand="block" @click="testInitialization" :disabled="testing">
            <ion-icon :icon="playOutline" slot="start"></ion-icon>
            Test Initialization
          </ion-button>
          
          <ion-button expand="block" @click="testTokenGeneration" :disabled="testing">
            <ion-icon :icon="keyOutline" slot="start"></ion-icon>
            Test Token Generation
          </ion-button>
          
          <ion-button expand="block" @click="testTokenStorage" :disabled="testing">
            <ion-icon :icon="saveOutline" slot="start"></ion-icon>
            Test Token Storage
          </ion-button>
          
          <ion-button expand="block" @click="testServerCommunication" :disabled="testing">
            <ion-icon :icon="cloudUploadOutline" slot="start"></ion-icon>
            Test Server Communication
          </ion-button>
        </ion-card-content>
      </ion-card>

      <ion-card>
        <ion-card-header>
          <ion-card-title>Current Status</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <ion-item>
            <ion-label>
              <h3>Supported</h3>
              <p>{{ isSupported ? 'Yes' : 'No' }}</p>
            </ion-label>
            <ion-icon 
              :icon="isSupported ? checkmarkCircleOutline : closeCircleOutline" 
              :color="isSupported ? 'success' : 'danger'"
              slot="end"
            ></ion-icon>
          </ion-item>
          
          <ion-item>
            <ion-label>
              <h3>Current Token</h3>
              <p>{{ token ? token.substring(0, 20) + '...' : 'No token' }}</p>
            </ion-label>
            <ion-button 
              v-if="token" 
              fill="clear" 
              @click="copyToken"
              slot="end"
            >
              <ion-icon :icon="copyOutline"></ion-icon>
            </ion-button>
          </ion-item>
          
          <ion-item>
            <ion-label>
              <h3>Last Error</h3>
              <p>{{ error ? error.message : 'None' }}</p>
            </ion-label>
            <ion-icon 
              :icon="error ? alertCircleOutline : checkmarkCircleOutline" 
              :color="error ? 'warning' : 'success'"
              slot="end"
            ></ion-icon>
          </ion-item>
        </ion-card-content>
      </ion-card>

      <ion-card>
        <ion-card-header>
          <ion-card-title>Error Statistics</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <ion-item>
            <ion-label>
              <h3>Error Count</h3>
              <p>{{ errorStats.errorCount }}</p>
            </ion-label>
          </ion-item>
          
          <ion-item>
            <ion-label>
              <h3>Error Rate Too High</h3>
              <p>{{ errorStats.isErrorRateTooHigh ? 'Yes' : 'No' }}</p>
            </ion-label>
          </ion-item>
          
          <ion-button expand="block" @click="getErrorReport" :disabled="testing">
            <ion-icon :icon="documentTextOutline" slot="start"></ion-icon>
            Generate Error Report
          </ion-button>
          
          <ion-button expand="block" @click="clearErrorStats" :disabled="testing" color="warning">
            <ion-icon :icon="trashOutline" slot="start"></ion-icon>
            Clear Error Statistics
          </ion-button>
        </ion-card-content>
      </ion-card>

      <ion-card>
        <ion-card-header>
          <ion-card-title>Advanced Testing</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <ion-button expand="block" @click="testBackgroundProcessing" :disabled="testing">
            <ion-icon :icon="layersOutline" slot="start"></ion-icon>
            Test Background Processing
          </ion-button>
          
          <ion-button expand="block" @click="testDeepLinking" :disabled="testing">
            <ion-icon :icon="linkOutline" slot="start"></ion-icon>
            Test Deep Linking
          </ion-button>
          
          <ion-button expand="block" @click="runFullTestSuite" :disabled="testing" color="secondary">
            <ion-icon :icon="checkboxOutline" slot="start"></ion-icon>
            Run Full Test Suite
          </ion-button>
        </ion-card-content>
      </ion-card>

      <ion-card>
        <ion-card-header>
          <ion-card-title>Test Results</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <div class="test-results">
            <div v-for="result in testResults" :key="result.id" class="test-result">
              <ion-icon 
                :icon="result.success ? checkmarkCircleOutline : closeCircleOutline"
                :color="result.success ? 'success' : 'danger'"
              ></ion-icon>
              <span class="test-name">{{ result.name }}</span>
              <span class="test-time">{{ result.timestamp }}</span>
            </div>
          </div>
          
          <ion-button expand="block" @click="clearResults" :disabled="testing" fill="clear">
            <ion-icon :icon="refreshOutline" slot="start"></ion-icon>
            Clear Results
          </ion-button>
        </ion-card-content>
      </ion-card>

      <ion-loading :is-open="testing" message="Running tests..."></ion-loading>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonButton,
  IonItem,
  IonLabel,
  IonIcon,
  IonLoading,
  toastController
} from '@ionic/vue';
import {
  playOutline,
  keyOutline,
  saveOutline,
  cloudUploadOutline,
  checkmarkCircleOutline,
  closeCircleOutline,
  copyOutline,
  alertCircleOutline,
  documentTextOutline,
  trashOutline,
  layersOutline,
  linkOutline,
  checkboxOutline,
  refreshOutline
} from 'ionicons/icons';
import { usePushNotifications } from '@/composables/usePushNotifications';

const {
  token,
  error,
  isSupported,
  service,
  getErrorStatistics,
  createErrorReport,
  clearErrorStatistics,
  processBackgroundData,
  handleDeepLink
} = usePushNotifications();

const testing = ref(false);
const errorStats = ref({
  errorCount: 0,
  isErrorRateTooHigh: false,
  lastError: null as string | null,
  lastErrorTime: 0
});

interface TestResult {
  id: string;
  name: string;
  success: boolean;
  timestamp: string;
  details?: string;
}

const testResults = ref<TestResult[]>([]);

onMounted(async () => {
  await updateErrorStats();
});

const addTestResult = (name: string, success: boolean, details?: string) => {
  testResults.value.unshift({
    id: Date.now().toString(),
    name,
    success,
    timestamp: new Date().toLocaleTimeString(),
    details
  });
};

const showToast = async (message: string, color: string = 'primary') => {
  const toast = await toastController.create({
    message,
    duration: 3000,
    color,
    position: 'bottom'
  });
  await toast.present();
};

const updateErrorStats = async () => {
  try {
    errorStats.value = await getErrorStatistics();
  } catch (error) {
    console.error('Failed to get error statistics:', error);
  }
};

const testInitialization = async () => {
  testing.value = true;
  try {
    await service.initialize();
    addTestResult('Initialization', true);
    await showToast('Initialization successful', 'success');
  } catch (error: any) {
    addTestResult('Initialization', false, error.message);
    await showToast('Initialization failed', 'danger');
  } finally {
    testing.value = false;
    await updateErrorStats();
  }
};

const testTokenGeneration = async () => {
  testing.value = true;
  try {
    const currentToken = await service.getCurrentToken();
    if (currentToken) {
      addTestResult('Token Generation', true, `Token: ${currentToken.substring(0, 20)}...`);
      await showToast('Token generated successfully', 'success');
    } else {
      addTestResult('Token Generation', false, 'No token returned');
      await showToast('No token generated', 'warning');
    }
  } catch (error: any) {
    addTestResult('Token Generation', false, error.message);
    await showToast('Token generation failed', 'danger');
  } finally {
    testing.value = false;
  }
};

const testTokenStorage = async () => {
  testing.value = true;
  try {
    const currentToken = await service.getCurrentToken();
    const storedToken = await service.getStoredToken();
    
    if (currentToken === storedToken) {
      addTestResult('Token Storage', true, 'Tokens match');
      await showToast('Token storage working correctly', 'success');
    } else {
      addTestResult('Token Storage', false, 'Token mismatch');
      await showToast('Token storage mismatch', 'warning');
    }
  } catch (error: any) {
    addTestResult('Token Storage', false, error.message);
    await showToast('Token storage test failed', 'danger');
  } finally {
    testing.value = false;
  }
};

const testServerCommunication = async () => {
  testing.value = true;
  try {
    const currentToken = await service.getCurrentToken();
    if (!currentToken) {
      throw new Error('No token available');
    }
    
    await service.sendTokenToServer(currentToken);
    const isTokenSent = await service.isTokenSentToServer();
    
    if (isTokenSent) {
      addTestResult('Server Communication', true, 'Token sent successfully');
      await showToast('Server communication successful', 'success');
    } else {
      addTestResult('Server Communication', false, 'Token not marked as sent');
      await showToast('Server communication issue', 'warning');
    }
  } catch (error: any) {
    addTestResult('Server Communication', false, error.message);
    await showToast('Server communication failed', 'danger');
  } finally {
    testing.value = false;
  }
};

const testBackgroundProcessing = async () => {
  testing.value = true;
  try {
    await processBackgroundData();
    addTestResult('Background Processing', true);
    await showToast('Background processing successful', 'success');
  } catch (error: any) {
    addTestResult('Background Processing', false, error.message);
    await showToast('Background processing failed', 'danger');
  } finally {
    testing.value = false;
  }
};

const testDeepLinking = async () => {
  testing.value = true;
  try {
    const testUrl = 'https://example.com/test';
    const success = await handleDeepLink(testUrl);
    
    if (success) {
      addTestResult('Deep Linking', true, `Handled: ${testUrl}`);
      await showToast('Deep linking successful', 'success');
    } else {
      addTestResult('Deep Linking', false, 'Deep link not handled');
      await showToast('Deep linking failed', 'warning');
    }
  } catch (error: any) {
    addTestResult('Deep Linking', false, error.message);
    await showToast('Deep linking test failed', 'danger');
  } finally {
    testing.value = false;
  }
};

const runFullTestSuite = async () => {
  testing.value = true;
  try {
    await testInitialization();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testTokenGeneration();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testTokenStorage();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testServerCommunication();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testBackgroundProcessing();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testDeepLinking();
    
    await showToast('Full test suite completed', 'success');
  } catch (error: any) {
    await showToast('Test suite failed', 'danger');
  } finally {
    testing.value = false;
  }
};

const getErrorReport = async () => {
  testing.value = true;
  try {
    const report = await createErrorReport();
    console.log('Error Report:', report);
    await showToast('Error report generated (check console)', 'primary');
  } catch (error: any) {
    await showToast('Failed to generate error report', 'danger');
  } finally {
    testing.value = false;
  }
};

const clearErrorStats = async () => {
  testing.value = true;
  try {
    await clearErrorStatistics();
    await updateErrorStats();
    await showToast('Error statistics cleared', 'success');
  } catch (error: any) {
    await showToast('Failed to clear error statistics', 'danger');
  } finally {
    testing.value = false;
  }
};

const copyToken = async () => {
  if (token.value) {
    try {
      await navigator.clipboard.writeText(token.value);
      await showToast('Token copied to clipboard', 'success');
    } catch (error: any) {
      console.log('Token:', token.value);
      await showToast('Token logged to console', 'primary');
    }
  }
};

const clearResults = () => {
  testResults.value = [];
};
</script>

<style scoped>
.test-results {
  max-height: 200px;
  overflow-y: auto;
}

.test-result {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--ion-color-light);
}

.test-result:last-child {
  border-bottom: none;
}

.test-result ion-icon {
  margin-right: 12px;
}

.test-name {
  flex: 1;
  font-weight: 500;
}

.test-time {
  font-size: 0.8em;
  color: var(--ion-color-medium);
}
</style>
