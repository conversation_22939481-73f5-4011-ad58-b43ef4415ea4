import { InAppBrowser } from '@capgo/inappbrowser';
import { WebContainerPersistenceService } from './web-container-persistence-service';

// TypeScript Interfaces for Debug Toolbar
export interface DebugToolbarConfig {
  enabled: boolean;
  position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  zIndex: number;
}

export interface DebugToolbarState {
  position: { x: number; y: number };
  visible: boolean;
  collapsed: boolean;
  lastInjectionTime: number;
}

export interface DebugMessage {
  requestId: string;
  type: 'logCookies' | 'logLocalStorage' | 'clearCookies' | 'clearLocalStorage' | 'refreshPage' | 'cookiesResponse' | 'localStorageResponse' | 'clearCookiesResponse' | 'clearLocalStorageResponse' | 'refreshPageResponse';
  success?: boolean;
  data?: any;
  error?: string;
}

/**
 * DebugToolbarService
 * 
 * Provides debugging capabilities for web container applications by injecting
 * a toolbar with cookie and localStorage logging functionality.
 * Uses bidirectional postMessage communication following established patterns.
 */
export class DebugToolbarService {
  private static readonly DEFAULT_CONFIG: DebugToolbarConfig = {
    enabled: import.meta.env.VITE_ENABLE_DEBUG_TOOLBAR === 'true',
    position: 'bottom-right',
    zIndex: 999999
  };

  private config: DebugToolbarConfig = DebugToolbarService.DEFAULT_CONFIG;
  private isInjected = false;
  private messageListeners: any[] = [];
  private readonly requestTimeout = 10000; // 10 seconds
  private toolbarState: DebugToolbarState = {
    position: { x: 0, y: 0 },
    visible: true,
    collapsed: false,
    lastInjectionTime: 0
  };
  private readonly minReinjectionInterval = 2000; // 2 seconds between re-injections
  private persistenceService: WebContainerPersistenceService;

  constructor() {
    this.persistenceService = new WebContainerPersistenceService();
    console.log('DebugToolbarService initialized:', { enabled: this.config.enabled });
  }

  /**
   * Check if debug toolbar is enabled
   */
  isEnabled(): boolean {
    return this.config.enabled;
  }

  /**
   * Inject debug toolbar into web container
   */
  async injectToolbar(): Promise<boolean> {
    if (!this.config.enabled || this.isInjected) {
      return false;
    }

    try {
      console.log('Injecting debug toolbar...');

      // Generate toolbar HTML and CSS
      const toolbarScript = this.generateToolbarScript();

      // Inject the toolbar script
      await InAppBrowser.executeScript({ code: toolbarScript });

      // Set up message listeners for toolbar communication
      await this.setupMessageListeners();

      this.isInjected = true;
      console.log('Debug toolbar injected successfully');
      return true;

    } catch (error) {
      console.error('Failed to inject debug toolbar:', error);
      return false;
    }
  }

  /**
   * Remove debug toolbar and cleanup
   */
  async removeToolbar(): Promise<void> {
    if (!this.isInjected) {
      return;
    }

    try {
      // Remove toolbar from DOM
      const removeScript = `
        (function() {
          const toolbar = document.getElementById('debug-toolbar-container');
          if (toolbar) {
            toolbar.remove();
          }
          // Clean up global references
          if (window._debugToolbarInjected) {
            delete window._debugToolbarInjected;
          }
        })();
      `;

      await InAppBrowser.executeScript({ code: removeScript });

      // Clean up message listeners
      this.cleanupListeners();

      this.isInjected = false;
      console.log('Debug toolbar removed successfully');

    } catch (error) {
      console.error('Failed to remove debug toolbar:', error);
    }
  }

  /**
   * Generate the complete toolbar script with HTML, CSS, and JavaScript
   */
  private generateToolbarScript(): string {
    const { position, zIndex } = this.config;
    
    // Determine positioning styles based on config
    const positionStyles = this.getPositionStyles(position);

    return `
      (function() {
        // Only inject toolbar once
        if (window._debugToolbarInjected) return;
        window._debugToolbarInjected = true;

        // Create toolbar HTML
        const toolbarHTML = \`
          <div id="debug-toolbar-container" style="
            position: fixed;
            ${positionStyles}
            z-index: ${zIndex};
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: 2px solid #4a5568;
            border-radius: 12px;
            padding: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            color: white;
            min-width: 200px;
            backdrop-filter: blur(10px);
            user-select: none;
            cursor: move;
          ">
            <div style="
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 8px;
              font-weight: 600;
              font-size: 12px;
              text-transform: uppercase;
              letter-spacing: 0.5px;
            ">
              <span>🔧 Debug Toolbar</span>
              <div style="display: flex; gap: 4px; align-items: center;">
                <button id="debug-toolbar-toggle" style="
                  background: rgba(255, 255, 255, 0.2);
                  border: none;
                  border-radius: 50%;
                  width: 20px;
                  height: 20px;
                  color: white;
                  cursor: pointer;
                  font-size: 10px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  transition: transform 0.2s ease;
                " title="Toggle toolbar">▼</button>
                <button id="debug-toolbar-close" style="
                  background: rgba(255, 255, 255, 0.2);
                  border: none;
                  border-radius: 50%;
                  width: 20px;
                  height: 20px;
                  color: white;
                  cursor: pointer;
                  font-size: 12px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                " title="Close toolbar">×</button>
              </div>
            </div>
            <div id="debug-toolbar-content" style="
              display: flex;
              flex-direction: column;
              gap: 8px;
              transition: all 0.3s ease;
              overflow: hidden;
            ">
              <button id="debug-log-cookies" style="
                background: rgba(255, 255, 255, 0.15);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 8px 12px;
                color: white;
                cursor: pointer;
                font-size: 13px;
                font-weight: 500;
                transition: all 0.2s ease;
              " onmouseover="this.style.background='rgba(255, 255, 255, 0.25)'"
                 onmouseout="this.style.background='rgba(255, 255, 255, 0.15)'">
                🍪 Log All Cookies
              </button>
              <button id="debug-log-localstorage" style="
                background: rgba(255, 255, 255, 0.15);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 8px 12px;
                color: white;
                cursor: pointer;
                font-size: 13px;
                font-weight: 500;
                transition: all 0.2s ease;
              " onmouseover="this.style.background='rgba(255, 255, 255, 0.25)'"
                 onmouseout="this.style.background='rgba(255, 255, 255, 0.15)'">
                💾 Log All LocalStorage
              </button>
              <button id="debug-clear-cookies" style="
                background: rgba(255, 87, 87, 0.2);
                border: 1px solid rgba(255, 87, 87, 0.4);
                border-radius: 6px;
                padding: 8px 12px;
                color: white;
                cursor: pointer;
                font-size: 13px;
                font-weight: 500;
                transition: all 0.2s ease;
              " onmouseover="this.style.background='rgba(255, 87, 87, 0.35)'"
                 onmouseout="this.style.background='rgba(255, 87, 87, 0.2)'">
                🗑️ Clear Cookies
              </button>
              <button id="debug-clear-localstorage" style="
                background: rgba(255, 87, 87, 0.2);
                border: 1px solid rgba(255, 87, 87, 0.4);
                border-radius: 6px;
                padding: 8px 12px;
                color: white;
                cursor: pointer;
                font-size: 13px;
                font-weight: 500;
                transition: all 0.2s ease;
              " onmouseover="this.style.background='rgba(255, 87, 87, 0.35)'"
                 onmouseout="this.style.background='rgba(255, 87, 87, 0.2)'">
                🗑️ Clear LocalStorage
              </button>
              <button id="debug-refresh-page" style="
                background: rgba(76, 175, 80, 0.2);
                border: 1px solid rgba(76, 175, 80, 0.4);
                border-radius: 6px;
                padding: 8px 12px;
                color: white;
                cursor: pointer;
                font-size: 13px;
                font-weight: 500;
                transition: all 0.2s ease;
              " onmouseover="this.style.background='rgba(76, 175, 80, 0.35)'"
                 onmouseout="this.style.background='rgba(76, 175, 80, 0.2)'">
                🔄 Refresh Page
              </button>
            </div>
          </div>
        \`;

        // Inject toolbar into DOM
        document.body.insertAdjacentHTML('beforeend', toolbarHTML);

        // Make toolbar draggable
        const toolbar = document.getElementById('debug-toolbar-container');
        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };

        toolbar.addEventListener('mousedown', (e) => {
          if (e.target.id === 'debug-toolbar-close' || e.target.tagName === 'BUTTON') return;
          isDragging = true;
          const rect = toolbar.getBoundingClientRect();
          dragOffset.x = e.clientX - rect.left;
          dragOffset.y = e.clientY - rect.top;
          toolbar.style.cursor = 'grabbing';
        });

        document.addEventListener('mousemove', (e) => {
          if (!isDragging) return;
          e.preventDefault();
          toolbar.style.left = (e.clientX - dragOffset.x) + 'px';
          toolbar.style.top = (e.clientY - dragOffset.y) + 'px';
          toolbar.style.right = 'auto';
          toolbar.style.bottom = 'auto';
        });

        document.addEventListener('mouseup', () => {
          if (isDragging) {
            isDragging = false;
            toolbar.style.cursor = 'move';
          }
        });

        // Close button functionality
        document.getElementById('debug-toolbar-close').addEventListener('click', () => {
          toolbar.remove();
        });

        // Toggle button functionality for collapsible toolbar
        let isCollapsed = false;
        const toggleButton = document.getElementById('debug-toolbar-toggle');
        const content = document.getElementById('debug-toolbar-content');

        toggleButton.addEventListener('click', () => {
          isCollapsed = !isCollapsed;

          if (isCollapsed) {
            content.style.height = '0px';
            content.style.marginTop = '0px';
            content.style.opacity = '0';
            toggleButton.innerHTML = '▶';
            toggleButton.style.transform = 'rotate(-90deg)';
          } else {
            content.style.height = 'auto';
            content.style.marginTop = '8px';
            content.style.opacity = '1';
            toggleButton.innerHTML = '▼';
            toggleButton.style.transform = 'rotate(0deg)';
          }
        });

        // Toast notification function
        function showToast(message, type = 'success') {
          const toast = document.createElement('div');
          toast.style.cssText = \`
            position: fixed;
            top: 20px;
            right: 20px;
            background: \${type === 'success' ? '#4CAF50' : '#f44336'};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            z-index: 1000000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
          \`;
          toast.textContent = message;

          document.body.appendChild(toast);

          // Animate in
          setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
          }, 10);

          // Remove after 3 seconds
          setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
              if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
              }
            }, 300);
          }, 3000);
        }

        // Set up message listener for native app communication
        window.addEventListener('messageFromNative', function(event) {
          if (!event.detail) return;

          const { requestId, type } = event.detail;

          if (type === 'logCookies') {
            handleLogCookies(requestId);
          } else if (type === 'logLocalStorage') {
            handleLogLocalStorage(requestId);
          } else if (type === 'clearCookies') {
            handleClearCookies(requestId);
          } else if (type === 'clearLocalStorage') {
            handleClearLocalStorage(requestId);
          } else if (type === 'refreshPage') {
            handleRefreshPage(requestId);
          } else if (type === 'checkToolbarPresence') {
            handleToolbarPresenceCheck(requestId);
          } else if (type === 'getToolbarState') {
            handleGetToolbarState(requestId);
          } else if (type === 'restoreToolbarState') {
            handleRestoreToolbarState(requestId, event.detail.state);
          }
        });

        // Cookie logging functionality
        function handleLogCookies(requestId) {
          try {
            const cookies = {};
            const cookieString = document.cookie;
            
            if (cookieString) {
              cookieString.split(';').forEach(cookie => {
                const [name, ...rest] = cookie.trim().split('=');
                if (name) {
                  cookies[name] = rest.join('=') || '';
                }
              });
            }

            // Send response back to native app
            window.mobileApp.postMessage({
              detail: {
                requestId: requestId,
                type: 'cookiesResponse',
                success: true,
                data: cookies
              }
            });
          } catch (error) {
            window.mobileApp.postMessage({
              detail: {
                requestId: requestId,
                type: 'cookiesResponse',
                success: false,
                error: error.message || 'Failed to extract cookies'
              }
            });
          }
        }

        // LocalStorage logging functionality
        function handleLogLocalStorage(requestId) {
          try {
            const localStorageData = {};
            
            for (let i = 0; i < localStorage.length; i++) {
              const key = localStorage.key(i);
              if (key) {
                localStorageData[key] = localStorage.getItem(key);
              }
            }

            // Send response back to native app
            window.mobileApp.postMessage({
              detail: {
                requestId: requestId,
                type: 'localStorageResponse',
                success: true,
                data: localStorageData
              }
            });
          } catch (error) {
            window.mobileApp.postMessage({
              detail: {
                requestId: requestId,
                type: 'localStorageResponse',
                success: false,
                error: error.message || 'Failed to extract localStorage'
              }
            });
          }
        }

        // Clear cookies functionality with comprehensive cleanup
        function handleClearCookies(requestId) {
          try {
            // Get all cookies
            const cookies = document.cookie.split(';');
            let clearedCount = 0;
            let totalClearAttempts = 0;

            // Enhanced cookie clearing with multiple domain/path combinations
            cookies.forEach(cookie => {
              const eqPos = cookie.indexOf('=');
              const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
              if (name) {
                const domain = window.location.hostname;
                const protocol = window.location.protocol;
                const paths = ['/', '/app', '/web', ''];

                // Clear for current domain with various paths
                paths.forEach(path => {
                  document.cookie = \`\${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=\${path}\`;
                  document.cookie = \`\${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=\${path};domain=\${domain}\`;
                  totalClearAttempts += 2;
                });

                // Clear for parent domain variations
                const parts = domain.split('.');
                if (parts.length > 1) {
                  const parentDomain = '.' + parts.slice(-2).join('.');
                  paths.forEach(path => {
                    document.cookie = \`\${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=\${path};domain=\${parentDomain}\`;
                    totalClearAttempts++;
                  });
                }

                // Clear for all possible subdomain variations
                if (parts.length > 2) {
                  for (let i = 1; i < parts.length - 1; i++) {
                    const subDomain = '.' + parts.slice(i).join('.');
                    paths.forEach(path => {
                      document.cookie = \`\${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=\${path};domain=\${subDomain}\`;
                      totalClearAttempts++;
                    });
                  }
                }

                // Clear with secure flag variations
                if (protocol === 'https:') {
                  paths.forEach(path => {
                    document.cookie = \`\${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=\${path};secure\`;
                    document.cookie = \`\${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=\${path};domain=\${domain};secure\`;
                    totalClearAttempts += 2;
                  });
                }

                clearedCount++;
              }
            });

            showToast(\`Comprehensive cookie clearing: \${clearedCount} cookies with \${totalClearAttempts} clear attempts\`, 'success');

            // Send response back to native app
            window.mobileApp.postMessage({
              detail: {
                requestId: requestId,
                type: 'clearCookiesResponse',
                success: true,
                data: {
                  clearedCount: clearedCount,
                  totalClearAttempts: totalClearAttempts,
                  comprehensive: true
                }
              }
            });
          } catch (error) {
            showToast('Failed to clear cookies', 'error');
            window.mobileApp.postMessage({
              detail: {
                requestId: requestId,
                type: 'clearCookiesResponse',
                success: false,
                error: error.message || 'Failed to clear cookies'
              }
            });
          }
        }

        // Clear localStorage functionality with comprehensive cleanup
        function handleClearLocalStorage(requestId) {
          try {
            const localStorageCount = localStorage.length;
            localStorage.clear();

            // Clear sessionStorage as well
            let sessionStorageCount = 0;
            try {
              sessionStorageCount = sessionStorage.length;
              sessionStorage.clear();
            } catch (sessionError) {
              console.warn('Failed to clear sessionStorage:', sessionError);
            }

            // Clear IndexedDB databases if present
            let indexedDBCleared = 0;
            try {
              if (window.indexedDB && window.indexedDB.databases) {
                window.indexedDB.databases().then(databases => {
                  databases.forEach(db => {
                    if (db.name) {
                      const deleteReq = window.indexedDB.deleteDatabase(db.name);
                      deleteReq.onsuccess = () => {
                        indexedDBCleared++;
                        console.log('🗑️ Cleared IndexedDB database:', db.name);
                      };
                      deleteReq.onerror = (e) => console.warn('Failed to clear IndexedDB:', db.name, e);
                    }
                  });
                }).catch(e => console.warn('Failed to enumerate IndexedDB:', e));
              }
            } catch (indexedError) {
              console.warn('IndexedDB cleanup failed:', indexedError);
            }

            const totalCleared = localStorageCount + sessionStorageCount;
            showToast(\`Cleared \${totalCleared} storage items (localStorage: \${localStorageCount}, sessionStorage: \${sessionStorageCount})\`, 'success');

            // Send response back to native app
            window.mobileApp.postMessage({
              detail: {
                requestId: requestId,
                type: 'clearLocalStorageResponse',
                success: true,
                data: {
                  clearedCount: totalCleared,
                  localStorageCount: localStorageCount,
                  sessionStorageCount: sessionStorageCount,
                  indexedDBCleared: indexedDBCleared,
                  comprehensive: true
                }
              }
            });
          } catch (error) {
            showToast('Failed to clear storage', 'error');
            window.mobileApp.postMessage({
              detail: {
                requestId: requestId,
                type: 'clearLocalStorageResponse',
                success: false,
                error: error.message || 'Failed to clear storage'
              }
            });
          }
        }

        // Refresh page functionality
        function handleRefreshPage(requestId) {
          try {
            showToast('Refreshing page...', 'success');

            // Send response back to native app before refresh
            window.mobileApp.postMessage({
              detail: {
                requestId: requestId,
                type: 'refreshPageResponse',
                success: true,
                data: { action: 'refresh' }
              }
            });

            // Small delay to ensure toast is visible before refresh
            setTimeout(() => {
              window.location.reload();
            }, 500);
          } catch (error) {
            showToast('Failed to refresh page', 'error');
            window.mobileApp.postMessage({
              detail: {
                requestId: requestId,
                type: 'refreshPageResponse',
                success: false,
                error: error.message || 'Failed to refresh page'
              }
            });
          }
        }

        // Toolbar presence check handler
        function handleToolbarPresenceCheck(requestId) {
          try {
            const toolbar = document.getElementById('debug-toolbar-container');
            const present = toolbar !== null;

            window.mobileApp.postMessage({
              detail: {
                requestId: requestId,
                type: 'toolbarPresenceResponse',
                present: present
              }
            });
          } catch (error) {
            window.mobileApp.postMessage({
              detail: {
                requestId: requestId,
                type: 'toolbarPresenceResponse',
                present: false
              }
            });
          }
        }

        // Get toolbar state handler
        function handleGetToolbarState(requestId) {
          try {
            const toolbar = document.getElementById('debug-toolbar-container');
            if (!toolbar) {
              window.mobileApp.postMessage({
                detail: {
                  requestId: requestId,
                  type: 'toolbarStateResponse',
                  success: false
                }
              });
              return;
            }

            const rect = toolbar.getBoundingClientRect();
            const content = document.getElementById('debug-toolbar-content');
            const state = {
              position: { x: rect.left, y: rect.top },
              visible: toolbar.style.display !== 'none',
              collapsed: content ? content.style.height === '0px' : false,
              lastInjectionTime: Date.now()
            };

            window.mobileApp.postMessage({
              detail: {
                requestId: requestId,
                type: 'toolbarStateResponse',
                success: true,
                state: state
              }
            });
          } catch (error) {
            window.mobileApp.postMessage({
              detail: {
                requestId: requestId,
                type: 'toolbarStateResponse',
                success: false
              }
            });
          }
        }

        // Restore toolbar state handler
        function handleRestoreToolbarState(requestId, state) {
          try {
            const toolbar = document.getElementById('debug-toolbar-container');
            if (!toolbar || !state) return;

            // Restore position if provided
            if (state.position && state.position.x !== 0 && state.position.y !== 0) {
              toolbar.style.left = state.position.x + 'px';
              toolbar.style.top = state.position.y + 'px';
              toolbar.style.right = 'auto';
              toolbar.style.bottom = 'auto';
            }

            // Restore visibility
            if (state.visible !== undefined) {
              toolbar.style.display = state.visible ? 'block' : 'none';
            }

            // Restore collapsed state
            if (state.collapsed !== undefined) {
              const content = document.getElementById('debug-toolbar-content');
              const toggleButton = document.getElementById('debug-toolbar-toggle');

              if (content && toggleButton) {
                if (state.collapsed) {
                  content.style.height = '0px';
                  content.style.marginTop = '0px';
                  content.style.opacity = '0';
                  toggleButton.innerHTML = '▶';
                  toggleButton.style.transform = 'rotate(-90deg)';
                } else {
                  content.style.height = 'auto';
                  content.style.marginTop = '8px';
                  content.style.opacity = '1';
                  toggleButton.innerHTML = '▼';
                  toggleButton.style.transform = 'rotate(0deg)';
                }
              }
            }

            console.log('🔧 Debug Toolbar: State restored', state);
          } catch (error) {
            console.error('🔧 Debug Toolbar: Failed to restore state', error);
          }
        }

        // Button click handlers
        document.getElementById('debug-log-cookies').addEventListener('click', () => {
          const requestId = 'cookies_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
          handleLogCookies(requestId);
        });

        document.getElementById('debug-log-localstorage').addEventListener('click', () => {
          const requestId = 'localStorage_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
          handleLogLocalStorage(requestId);
        });

        document.getElementById('debug-clear-cookies').addEventListener('click', () => {
          const requestId = 'clearCookies_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
          handleClearCookies(requestId);
        });

        document.getElementById('debug-clear-localstorage').addEventListener('click', () => {
          const requestId = 'clearLocalStorage_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
          handleClearLocalStorage(requestId);
        });

        document.getElementById('debug-refresh-page').addEventListener('click', () => {
          const requestId = 'refreshPage_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
          handleRefreshPage(requestId);
        });

        console.log('🔧 Debug Toolbar: Ready! Use the floating toolbar to log/clear cookies, localStorage, and refresh page.');
      })();
    `;
  }

  /**
   * Get CSS positioning styles based on position config
   */
  private getPositionStyles(position: string): string {
    switch (position) {
      case 'top-left':
        return 'top: 20px; left: 20px;';
      case 'top-right':
        return 'top: 20px; right: 20px;';
      case 'bottom-left':
        return 'bottom: 20px; left: 20px;';
      case 'bottom-right':
      default:
        return 'bottom: 20px; right: 20px;';
    }
  }

  /**
   * Set up message listeners for toolbar communication
   */
  private async setupMessageListeners(): Promise<void> {
    try {
      // Listen for responses from web container
      const listener = await InAppBrowser.addListener('messageFromWebview', (event) => {
        if (!event.detail) return;

        const { requestId, type, success, data, error } = event.detail;

        if (type === 'cookiesResponse') {
          this.handleCookiesResponse(requestId, success, data, error);
        } else if (type === 'localStorageResponse') {
          this.handleLocalStorageResponse(requestId, success, data, error);
        } else if (type === 'clearCookiesResponse') {
          this.handleClearCookiesResponse(requestId, success, data, error);
        } else if (type === 'clearLocalStorageResponse') {
          this.handleClearLocalStorageResponse(requestId, success, data, error);
        } else if (type === 'refreshPageResponse') {
          this.handleRefreshPageResponse(requestId, success, data, error);
        }
      });

      this.messageListeners.push(listener);
      console.log('Debug toolbar message listeners set up');

    } catch (error) {
      console.error('Failed to set up debug toolbar message listeners:', error);
    }
  }

  /**
   * Handle cookies response from web container
   */
  private handleCookiesResponse(requestId: string, success: boolean, data: any, error?: string): void {
    if (success && data) {
      console.group('🍪 Debug Toolbar: All Cookies');
      console.log('Request ID:', requestId);
      console.log('Timestamp:', new Date().toISOString());
      console.log('Total Cookies:', Object.keys(data).length);

      if (Object.keys(data).length === 0) {
        console.log('No cookies found on this page');
      } else {
        console.table(data);

        // Also log individual cookies for easier inspection
        Object.entries(data).forEach(([name, value]) => {
          console.log(`${name}:`, value);
        });
      }
      console.groupEnd();
    } else {
      console.error('🍪 Debug Toolbar: Failed to extract cookies:', error || 'Unknown error');
    }
  }

  /**
   * Handle localStorage response from web container
   */
  private handleLocalStorageResponse(requestId: string, success: boolean, data: any, error?: string): void {
    if (success && data) {
      console.group('💾 Debug Toolbar: All LocalStorage');
      console.log('Request ID:', requestId);
      console.log('Timestamp:', new Date().toISOString());
      console.log('Total Items:', Object.keys(data).length);

      if (Object.keys(data).length === 0) {
        console.log('No localStorage items found on this page');
      } else {
        console.table(data);

        // Also log individual items with size information
        Object.entries(data).forEach(([key, value]) => {
          const size = new Blob([String(value)]).size;
          console.log(`${key} (${size} bytes):`, value);
        });
      }
      console.groupEnd();
    } else {
      console.error('💾 Debug Toolbar: Failed to extract localStorage:', error || 'Unknown error');
    }
  }

  /**
   * Handle comprehensive clear cookies response from web container
   */
  private handleClearCookiesResponse(requestId: string, success: boolean, data: any, error?: string): void {
    if (success && data) {
      console.group('🗑️ Debug Toolbar: Comprehensive Cookies Cleared');
      console.log('Request ID:', requestId);
      console.log('Timestamp:', new Date().toISOString());
      console.log('Cookies Cleared:', data.clearedCount || 0);

      if (data.comprehensive) {
        console.log('Total Clear Attempts:', data.totalClearAttempts || 0);
        console.log('Persistence Layer: Cleared');
        console.log('Enhanced Domain/Path Clearing: Applied');
      } else {
        console.log('Legacy Clear Count:', data.clearedCount || 0);
      }

      console.groupEnd();
    } else {
      console.error('🗑️ Debug Toolbar: Failed to clear cookies:', error || 'Unknown error');
    }
  }

  /**
   * Handle comprehensive clear localStorage response from web container
   */
  private handleClearLocalStorageResponse(requestId: string, success: boolean, data: any, error?: string): void {
    if (success && data) {
      console.group('🗑️ Debug Toolbar: Comprehensive Storage Cleared');
      console.log('Request ID:', requestId);
      console.log('Timestamp:', new Date().toISOString());
      console.log('Total Cleared:', data.clearedCount || 0);

      if (data.comprehensive) {
        console.log('LocalStorage Items:', data.localStorageCount || 0);
        console.log('SessionStorage Items:', data.sessionStorageCount || 0);
        console.log('IndexedDB Databases:', data.indexedDBCleared || 0);
        console.log('Persistence Layer: Cleared');
      } else {
        console.log('Legacy Clear Count:', data.clearedCount || 0);
      }

      console.groupEnd();
    } else {
      console.error('🗑️ Debug Toolbar: Failed to clear storage:', error || 'Unknown error');
    }
  }

  /**
   * Handle refresh page response from web container
   */
  private handleRefreshPageResponse(requestId: string, success: boolean, data: any, error?: string): void {
    if (success && data) {
      console.group('🔄 Debug Toolbar: Page Refresh Initiated');
      console.log('Request ID:', requestId);
      console.log('Timestamp:', new Date().toISOString());
      console.log('Action:', data.action || 'refresh');
      console.groupEnd();
    } else {
      console.error('🔄 Debug Toolbar: Failed to refresh page:', error || 'Unknown error');
    }
  }

  /**
   * Trigger cookie logging from native side
   */
  async logCookies(): Promise<void> {
    if (!this.isInjected) {
      console.warn('Debug toolbar not injected, cannot log cookies');
      return;
    }

    try {
      const requestId = `cookies_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      await InAppBrowser.postMessage({
        detail: {
          requestId: requestId,
          type: 'logCookies'
        }
      });

      console.log('Cookie logging request sent:', requestId);
    } catch (error) {
      console.error('Failed to trigger cookie logging:', error);
    }
  }

  /**
   * Trigger localStorage logging from native side
   */
  async logLocalStorage(): Promise<void> {
    if (!this.isInjected) {
      console.warn('Debug toolbar not injected, cannot log localStorage');
      return;
    }

    try {
      const requestId = `localStorage_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      await InAppBrowser.postMessage({
        detail: {
          requestId: requestId,
          type: 'logLocalStorage'
        }
      });

      console.log('LocalStorage logging request sent:', requestId);
    } catch (error) {
      console.error('Failed to trigger localStorage logging:', error);
    }
  }

  /**
   * Trigger comprehensive cookie clearing from native side
   */
  async clearCookies(): Promise<void> {
    if (!this.isInjected) {
      console.warn('Debug toolbar not injected, cannot clear cookies');
      return;
    }

    try {
      const requestId = `clearCookies_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // Clear persistence layer backup data first
      try {
        await this.persistenceService.clearStoredState();
        console.log('🗑️ Debug Toolbar: Cleared cookie backup data from persistence layer');
      } catch (persistenceError) {
        console.warn('🗑️ Debug Toolbar: Failed to clear cookie persistence data:', persistenceError);
        // Continue with web container clearing even if persistence clearing fails
      }

      // Send clearing request to web container
      await InAppBrowser.postMessage({
        detail: {
          requestId: requestId,
          type: 'clearCookies'
        }
      });

      console.log('Comprehensive cookie clearing request sent:', requestId);
    } catch (error) {
      console.error('Failed to trigger comprehensive cookie clearing:', error);
    }
  }

  /**
   * Trigger comprehensive localStorage clearing from native side
   */
  async clearLocalStorage(): Promise<void> {
    if (!this.isInjected) {
      console.warn('Debug toolbar not injected, cannot clear localStorage');
      return;
    }

    try {
      const requestId = `clearLocalStorage_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // Clear persistence layer backup data first
      try {
        await this.persistenceService.clearStoredState();
        console.log('🗑️ Debug Toolbar: Cleared localStorage backup data from persistence layer');
      } catch (persistenceError) {
        console.warn('🗑️ Debug Toolbar: Failed to clear localStorage persistence data:', persistenceError);
        // Continue with web container clearing even if persistence clearing fails
      }

      // Send clearing request to web container
      await InAppBrowser.postMessage({
        detail: {
          requestId: requestId,
          type: 'clearLocalStorage'
        }
      });

      console.log('Comprehensive localStorage clearing request sent:', requestId);
    } catch (error) {
      console.error('Failed to trigger comprehensive localStorage clearing:', error);
    }
  }

  /**
   * Trigger page refresh from native side
   */
  async refreshPage(): Promise<void> {
    if (!this.isInjected) {
      console.warn('Debug toolbar not injected, cannot refresh page');
      return;
    }

    try {
      const requestId = `refreshPage_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      await InAppBrowser.postMessage({
        detail: {
          requestId: requestId,
          type: 'refreshPage'
        }
      });

      console.log('Page refresh request sent:', requestId);
    } catch (error) {
      console.error('Failed to trigger page refresh:', error);
    }
  }



  /**
   * Clean up message listeners
   */
  private cleanupListeners(): void {
    try {
      this.messageListeners.forEach(listener => {
        if (listener && typeof listener.remove === 'function') {
          listener.remove();
        }
      });
      this.messageListeners = [];
      console.log('Debug toolbar listeners cleaned up');
    } catch (error) {
      console.error('Error cleaning up debug toolbar listeners:', error);
    }
  }

  /**
   * Check if toolbar is still present in the web container DOM
   */
  async isToolbarPresent(): Promise<boolean> {
    if (!this.config.enabled || !this.isInjected) {
      return false;
    }

    try {
      const requestId = `checkToolbar_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // Set up response listener
      const responsePromise = new Promise<boolean>((resolve) => {
        let listenerHandle: any = null;

        const timeout = setTimeout(() => {
          if (listenerHandle) {
            listenerHandle.remove();
          }
          resolve(false); // Assume not present if no response
        }, 3000); // 3 second timeout for presence check

        InAppBrowser.addListener('messageFromWebview', (event) => {
          if (event.detail && event.detail.requestId === requestId && event.detail.type === 'toolbarPresenceResponse') {
            clearTimeout(timeout);
            if (listenerHandle) {
              listenerHandle.remove();
            }
            resolve(event.detail.present === true);
          }
        }).then((handle) => {
          listenerHandle = handle;
        });

        // Send presence check request
        InAppBrowser.postMessage({
          detail: {
            requestId: requestId,
            type: 'checkToolbarPresence'
          }
        }).catch(() => {
          clearTimeout(timeout);
          if (listenerHandle) {
            listenerHandle.remove();
          }
          resolve(false);
        });
      });

      return await responsePromise;
    } catch (error) {
      console.error('Failed to check toolbar presence:', error);
      return false;
    }
  }

  /**
   * Preserve current toolbar state (position, visibility)
   */
  async preserveToolbarState(): Promise<void> {
    if (!this.config.enabled || !this.isInjected) {
      return;
    }

    try {
      const requestId = `getToolbarState_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // Set up response listener
      const responsePromise = new Promise<DebugToolbarState | null>((resolve) => {
        let listenerHandle: any = null;

        const timeout = setTimeout(() => {
          if (listenerHandle) {
            listenerHandle.remove();
          }
          resolve(null);
        }, 3000);

        InAppBrowser.addListener('messageFromWebview', (event) => {
          if (event.detail && event.detail.requestId === requestId && event.detail.type === 'toolbarStateResponse') {
            clearTimeout(timeout);
            if (listenerHandle) {
              listenerHandle.remove();
            }
            resolve(event.detail.success ? event.detail.state : null);
          }
        }).then((handle) => {
          listenerHandle = handle;
        });

        // Send state request
        InAppBrowser.postMessage({
          detail: {
            requestId: requestId,
            type: 'getToolbarState'
          }
        }).catch(() => {
          clearTimeout(timeout);
          if (listenerHandle) {
            listenerHandle.remove();
          }
          resolve(null);
        });
      });

      const state = await responsePromise;
      if (state) {
        this.toolbarState = { ...this.toolbarState, ...state };
        console.log('Toolbar state preserved:', this.toolbarState);
      }
    } catch (error) {
      console.error('Failed to preserve toolbar state:', error);
    }
  }

  /**
   * Re-inject toolbar while preserving existing state
   */
  async reinjectToolbar(): Promise<boolean> {
    if (!this.config.enabled) {
      return false;
    }

    // Debounce re-injection attempts
    const now = Date.now();
    if (now - this.toolbarState.lastInjectionTime < this.minReinjectionInterval) {
      console.log('Re-injection debounced, too soon since last attempt');
      return false;
    }

    try {
      console.log('Re-injecting debug toolbar...');

      // Preserve current state before re-injection
      if (this.isInjected) {
        await this.preserveToolbarState();
      }

      // Reset injection flag to allow re-injection
      this.isInjected = false;

      // Inject toolbar with preserved state
      const success = await this.injectToolbar();

      if (success) {
        this.toolbarState.lastInjectionTime = now;

        // Restore preserved state after a short delay
        setTimeout(async () => {
          await this.restoreToolbarState();
        }, 500);

        console.log('Debug toolbar re-injected successfully');
      }

      return success;
    } catch (error) {
      console.error('Failed to re-inject debug toolbar:', error);
      return false;
    }
  }

  /**
   * Restore preserved toolbar state after re-injection
   */
  private async restoreToolbarState(): Promise<void> {
    if (!this.config.enabled || !this.isInjected) {
      return;
    }

    try {
      const requestId = `restoreToolbarState_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      await InAppBrowser.postMessage({
        detail: {
          requestId: requestId,
          type: 'restoreToolbarState',
          state: this.toolbarState
        }
      });

      console.log('Toolbar state restoration requested');
    } catch (error) {
      console.error('Failed to restore toolbar state:', error);
    }
  }

  /**
   * Get service status for debugging
   */
  getStatus(): { enabled: boolean; injected: boolean; config: DebugToolbarConfig; state: DebugToolbarState } {
    return {
      enabled: this.config.enabled,
      injected: this.isInjected,
      config: this.config,
      state: this.toolbarState
    };
  }
}
