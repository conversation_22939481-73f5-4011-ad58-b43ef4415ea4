import { fcmError<PERSON>and<PERSON>, LogLevel } from './fcm-error-handler';

/**
 * Deep link route interface
 */
export interface DeepLinkRoute {
  path: string;
  params?: Record<string, any>;
  query?: Record<string, any>;
  hash?: string;
}

/**
 * Deep link handler interface
 */
export interface DeepLinkHandler {
  canHandle(url: string): boolean;
  handle(url: string): Promise<DeepLinkRoute | null>;
}

/**
 * Deep linking service for handling notification-based navigation
 * Provides consistent deep linking functionality across iOS and Android
 */
export class DeepLinkService {
  private static instance: DeepLinkService;
  private handlers: DeepLinkHandler[] = [];
  private baseUrl: string = '';

  /**
   * Get singleton instance
   */
  public static getInstance(): DeepLinkService {
    if (!DeepLinkService.instance) {
      DeepLinkService.instance = new DeepLinkService();
    }
    return DeepLinkService.instance;
  }

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor() {}

  /**
   * Set the base URL for the application
   */
  public setBaseUrl(baseUrl: string): void {
    this.baseUrl = baseUrl;
    fcmErrorHandler.log(LogLevel.DEBUG, `Deep link base URL set: ${baseUrl}`);
  }

  /**
   * Register a deep link handler
   */
  public registerHandler(handler: DeepLinkHandler): void {
    this.handlers.push(handler);
    fcmErrorHandler.log(LogLevel.DEBUG, 'Deep link handler registered');
  }

  /**
   * Remove a deep link handler
   */
  public removeHandler(handler: DeepLinkHandler): void {
    this.handlers = this.handlers.filter(h => h !== handler);
    fcmErrorHandler.log(LogLevel.DEBUG, 'Deep link handler removed');
  }

  /**
   * Handle a deep link URL
   */
  public async handleDeepLink(url: string): Promise<boolean> {
    fcmErrorHandler.log(LogLevel.INFO, `Handling deep link: ${url}`);

    try {
      // Find a handler that can process this URL
      for (const handler of this.handlers) {
        if (handler.canHandle(url)) {
          const route = await handler.handle(url);
          if (route) {
            await this.navigateToRoute(route);
            fcmErrorHandler.log(LogLevel.INFO, `Deep link handled successfully: ${route.path}`);
            return true;
          }
        }
      }

      // If no handler found, try default handling
      const route = await this.parseDefaultDeepLink(url);
      if (route) {
        await this.navigateToRoute(route);
        fcmErrorHandler.log(LogLevel.INFO, `Default deep link handled: ${route.path}`);
        return true;
      }

      fcmErrorHandler.log(LogLevel.WARNING, `No handler found for deep link: ${url}`);
      return false;
    } catch (error) {
      fcmErrorHandler.log(LogLevel.ERROR, 'Error handling deep link', error);
      return false;
    }
  }

  /**
   * Parse a default deep link format
   */
  private async parseDefaultDeepLink(url: string): Promise<DeepLinkRoute | null> {
    try {
      const urlObj = new URL(url);
      
      // Extract path, removing leading slash
      const path = urlObj.pathname.startsWith('/') ? urlObj.pathname.substring(1) : urlObj.pathname;
      
      // Extract query parameters
      const query: Record<string, any> = {};
      urlObj.searchParams.forEach((value, key) => {
        query[key] = value;
      });

      // Extract hash
      const hash = urlObj.hash ? urlObj.hash.substring(1) : undefined;

      return {
        path: path || '/',
        query: Object.keys(query).length > 0 ? query : undefined,
        hash
      };
    } catch (error) {
      fcmErrorHandler.log(LogLevel.ERROR, 'Error parsing default deep link', error);
      return null;
    }
  }

  /**
   * Navigate to a route
   * This should be implemented to work with your router
   */
  private async navigateToRoute(route: DeepLinkRoute): Promise<void> {
    fcmErrorHandler.log(LogLevel.DEBUG, `Navigating to route: ${route.path}`, route);

    // TODO: Implement actual navigation logic
    // This would typically use Vue Router or similar
    // Example:
    // import router from '@/router';
    // await router.push({
    //   path: route.path,
    //   query: route.query,
    //   hash: route.hash
    // });

    // For now, just log the navigation
    console.log('Would navigate to:', route);
  }

  /**
   * Handle notification-based deep link
   */
  public async handleNotificationDeepLink(data: any): Promise<boolean> {
    fcmErrorHandler.log(LogLevel.INFO, 'Handling notification deep link', data);

    try {
      // Check for different deep link formats in notification data
      let url: string | null = null;

      if (data.deepLink) {
        url = data.deepLink;
      } else if (data.url) {
        url = data.url;
      } else if (data.route) {
        // Convert route to URL format
        url = this.routeToUrl(data.route, data.params, data.query);
      } else if (data.path) {
        // Convert path to URL format
        url = this.pathToUrl(data.path, data.params, data.query);
      }

      if (url) {
        return await this.handleDeepLink(url);
      }

      fcmErrorHandler.log(LogLevel.WARNING, 'No deep link found in notification data');
      return false;
    } catch (error) {
      fcmErrorHandler.log(LogLevel.ERROR, 'Error handling notification deep link', error);
      return false;
    }
  }

  /**
   * Convert route to URL
   */
  private routeToUrl(route: string, params?: any, query?: any): string {
    let url = this.baseUrl + (route.startsWith('/') ? route : '/' + route);

    // Add query parameters
    if (query && Object.keys(query).length > 0) {
      const queryString = new URLSearchParams(query).toString();
      url += '?' + queryString;
    }

    return url;
  }

  /**
   * Convert path to URL
   */
  private pathToUrl(path: string, params?: any, query?: any): string {
    return this.routeToUrl(path, params, query);
  }

  /**
   * Create a deep link URL
   */
  public createDeepLink(path: string, params?: any, query?: any): string {
    return this.routeToUrl(path, params, query);
  }

  /**
   * Validate if a URL is a valid deep link
   */
  public isValidDeepLink(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
}

/**
 * Default deep link handler for common patterns
 */
export class DefaultDeepLinkHandler implements DeepLinkHandler {
  canHandle(url: string): boolean {
    // Handle URLs that start with the app's scheme or are HTTP(S) URLs
    return url.startsWith('http://') || url.startsWith('https://') || url.includes('://');
  }

  async handle(url: string): Promise<DeepLinkRoute | null> {
    try {
      const urlObj = new URL(url);
      
      const path = urlObj.pathname.startsWith('/') ? urlObj.pathname.substring(1) : urlObj.pathname;
      
      const query: Record<string, any> = {};
      urlObj.searchParams.forEach((value, key) => {
        query[key] = value;
      });

      return {
        path: path || '/',
        query: Object.keys(query).length > 0 ? query : undefined,
        hash: urlObj.hash ? urlObj.hash.substring(1) : undefined
      };
    } catch (error) {
      fcmErrorHandler.log(LogLevel.ERROR, 'Error handling default deep link', error);
      return null;
    }
  }
}

// Export singleton instance
export const deepLinkService = DeepLinkService.getInstance();

// Register default handler
deepLinkService.registerHandler(new DefaultDeepLinkHandler());
