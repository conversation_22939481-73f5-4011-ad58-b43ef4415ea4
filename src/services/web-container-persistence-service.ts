import { Preferences } from "@capacitor/preferences";
import { InAppBrowser } from "@capgo/inappbrowser";

// TypeScript Interfaces for State Management
export interface WebContainerState {
  version: string;
  timestamp: number;
  url: string;
  localStorage: Record<string, string>;
  cookies: Record<string, string>;
  metadata: StateMetadata;
}

export interface StateMetadata {
  userAgent: string;
  domain: string;
  appVersion: string;
}

export interface PersistenceConfig {
  enabled: boolean;
  maxStateAge: number; // milliseconds
  maxStateSize: number; // bytes
  excludePatterns: string[]; // regex patterns for sensitive data
}

/**
 * WebContainerPersistenceService
 *
 * Provides state persistence and restoration for web container applications.
 * Handles localStorage and cookies using Capacitor's native storage capabilities
 * and robust postMessage communication patterns.
 */
export class WebContainerPersistenceService {
  private static readonly STORAGE_KEY = "webcontainer_state";
  private static readonly BACKUP_KEY = "webcontainer_state_backup";
  private static readonly CURRENT_VERSION = "1.0.0";

  private static readonly DEFAULT_CONFIG: PersistenceConfig = {
    enabled: import.meta.env.VITE_PERSISTENCE_ENABLED === "true",
    maxStateAge:
      (parseInt(import.meta.env.VITE_PERSISTENCE_MAX_AGE_DAYS) || 30) *
      24 *
      60 *
      60 *
      1000,
    maxStateSize:
      (parseInt(import.meta.env.VITE_PERSISTENCE_MAX_SIZE_MB) || 5) *
      1024 *
      1024,
    excludePatterns: (import.meta.env.VITE_PERSISTENCE_EXCLUDE_PATTERNS || "")
      .split(",")
      .map((pattern: string) => pattern.trim())
      .filter((pattern: string) => pattern.length > 0),
  };

  private config: PersistenceConfig =
    WebContainerPersistenceService.DEFAULT_CONFIG;
  private isOperationInProgress = false;
  private lastCaptureTime = 0;
  private readonly minCaptureInterval = 5000; // 5 seconds

  constructor() {
    this.loadConfig();
  }

  /**
   * Initialize the persistence service
   */
  async initialize(): Promise<void> {
    try {
      await this.loadConfig();
      console.log("WebContainerPersistenceService initialized");
    } catch (error) {
      console.error("Failed to initialize persistence service:", error);
    }
  }

  /**
   * Capture current web container state
   */
  async captureState(currentUrl: string): Promise<boolean> {
    if (!this.config.enabled || !currentUrl || this.isOperationInProgress) {
      return false;
    }

    // Debounce rapid captures
    const now = Date.now();
    if (now - this.lastCaptureTime < this.minCaptureInterval) {
      return false;
    }

    this.isOperationInProgress = true;
    this.lastCaptureTime = now;

    try {
      console.log("Capturing web container state...");

      // Extract state data
      const [localStorage, cookies] = await Promise.all([
        this.extractLocalStorage(),
        this.extractCookies(currentUrl),
      ]);

      // Build state object
      const state: WebContainerState = {
        version: WebContainerPersistenceService.CURRENT_VERSION,
        timestamp: now,
        url: currentUrl,
        localStorage: localStorage || {},
        cookies: cookies || {},
        metadata: {
          userAgent: navigator.userAgent,
          domain: new URL(currentUrl).hostname,
          appVersion: "1.0.0", // TODO: Get from app config
        },
      };

      // Validate and store state
      const success = await this.validateAndStoreState(state);

      if (success) {
        console.log("State captured successfully");
      } else {
        console.warn("State capture validation failed");
      }

      return success;
    } catch (error) {
      console.error("State capture failed:", error);
      return false;
    } finally {
      this.isOperationInProgress = false;
    }
  }

  /**
   * Restore web container state
   */
  async restoreState(currentUrl: string): Promise<boolean> {
    if (!this.config.enabled || !currentUrl || this.isOperationInProgress) {
      return false;
    }

    this.isOperationInProgress = true;

    try {
      console.log("Restoring web container state...");

      // Retrieve and validate stored state
      const state = await this.retrieveAndValidateState();
      if (!state) {
        console.log("No valid state found for restoration");
        return false;
      }

      // Check URL compatibility
      if (!this.isUrlCompatible(state.url, currentUrl)) {
        console.log("Stored state URL incompatible with current URL");
        return false;
      }

      // Restore data in sequence (order matters for dependencies)
      const restorationSteps = [
        () => this.restoreLocalStorage(state.localStorage),
        () => this.restoreCookies(state.cookies),
      ];

      let successCount = 0;
      for (const step of restorationSteps) {
        try {
          await step();
          successCount++;
        } catch (error) {
          console.warn("Restoration step failed:", error);
          // Continue with other steps - partial restoration is better than none
        }
      }

      const success = successCount > 0;
      if (success) {
        console.log(
          `State restoration completed: ${successCount}/${restorationSteps.length} steps successful`
        );
      }

      return success;
    } catch (error) {
      console.error("State restoration failed:", error);
      return false;
    } finally {
      this.isOperationInProgress = false;
    }
  }

  /**
   * Clear all stored state data
   */
  async clearStoredState(): Promise<void> {
    try {
      await Promise.all([
        Preferences.remove({ key: WebContainerPersistenceService.STORAGE_KEY }),
        Preferences.remove({ key: WebContainerPersistenceService.BACKUP_KEY }),
      ]);
      console.log("Stored state cleared");
    } catch (error) {
      console.error("Failed to clear stored state:", error);
    }
  }

  /**
   * Get current persistence configuration (read-only)
   */
  getConfig(): Readonly<PersistenceConfig> {
    return { ...this.config };
  }

  /**
   * Check if persistence is currently enabled
   */
  isEnabled(): boolean {
    return this.config.enabled;
  }

  /**
   * Get persistence status for debugging
   */
  getStatus(): string {
    if (!this.config.enabled) {
      return "Persistence is disabled via environment configuration";
    }

    const ageDays = Math.floor(this.config.maxStateAge / (24 * 60 * 60 * 1000));
    const sizeMB = Math.floor(this.config.maxStateSize / (1024 * 1024));

    return `Persistence enabled: ${ageDays} days retention, ${sizeMB}MB max size, ${this.config.excludePatterns.length} exclusion patterns`;
  }

  /**
   * Check if an operation is currently in progress
   */
  getOperationStatus(): boolean {
    return this.isOperationInProgress;
  }

  // Private helper methods for configuration and data extraction
  private async loadConfig(): Promise<void> {
    // Configuration is now loaded from environment variables at startup
    this.config = WebContainerPersistenceService.DEFAULT_CONFIG;
    console.log("Persistence configuration loaded from environment:", {
      enabled: this.config.enabled,
      maxStateAge: `${Math.floor(
        this.config.maxStateAge / (24 * 60 * 60 * 1000)
      )} days`,
      maxStateSize: `${Math.floor(
        this.config.maxStateSize / (1024 * 1024)
      )} MB`,
      excludePatterns: this.config.excludePatterns.length,
    });
  }

  private async extractLocalStorage(): Promise<Record<string, string> | null> {
    try {
      // Generate unique request ID for message correlation
      const requestId = `localStorage_${Date.now()}_${Math.random()
        .toString(36)
        .substring(2, 11)}`;

      // Set up message listener for the response
      const responsePromise = new Promise<Record<string, string> | null>(
        (resolve, reject) => {
          let listenerHandle: any = null;

          const timeout = setTimeout(() => {
            if (listenerHandle) {
              listenerHandle.remove();
            }
            reject(new Error("localStorage extraction timeout"));
          }, 10000); // 10 second timeout

          // Listen for response from web container
          InAppBrowser.addListener("messageFromWebview", (event) => {
            if (
              event.detail &&
              event.detail.requestId === requestId &&
              event.detail.type === "localStorageResponse"
            ) {
              clearTimeout(timeout);
              if (listenerHandle) {
                listenerHandle.remove();
              }

              if (event.detail.success) {
                resolve(event.detail.data || {});
              } else {
                reject(
                  new Error(
                    event.detail.error || "Failed to extract localStorage"
                  )
                );
              }
            }
          }).then((handle) => {
            listenerHandle = handle;
          });

          // Inject message listener script into web container if not already present
          const listenerScript = `
          (function() {
            // Only inject listener once
            if (window._localStorageListenerInjected) return;
            window._localStorageListenerInjected = true;

            // Listen for localStorage extraction requests from native app
            window.addEventListener('messageFromNative', function(event) {
              if (event.detail && event.detail.type === 'getLocalStorage') {
                try {
                  const data = {};
                  for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key) {
                      data[key] = localStorage.getItem(key);
                    }
                  }

                  // Send response back to native app
                  window.mobileApp.postMessage({
                    detail: {
                      requestId: event.detail.requestId,
                      type: 'localStorageResponse',
                      success: true,
                      data: data
                    }
                  });
                } catch (error) {
                  // Send error response back to native app
                  window.mobileApp.postMessage({
                    detail: {
                      requestId: event.detail.requestId,
                      type: 'localStorageResponse',
                      success: false,
                      error: error.message || 'Failed to extract localStorage'
                    }
                  });
                }
              }
            });
          })();
        `;

          // Inject the listener script first
          InAppBrowser.executeScript({ code: listenerScript })
            .then(() => {
              // Send the localStorage extraction request
              InAppBrowser.postMessage({
                detail: {
                  requestId: requestId,
                  type: "getLocalStorage",
                },
              });
            })
            .catch((error) => {
              clearTimeout(timeout);
              if (listenerHandle) {
                listenerHandle.remove();
              }
              reject(error);
            });
        }
      );

      return await responsePromise;
    } catch (error) {
      console.error("Failed to extract localStorage:", error);
      return null;
    }
  }

  private async extractCookies(
    url: string
  ): Promise<Record<string, string> | null> {
    try {
      const cookies = await InAppBrowser.getCookies({
        url: url,
        includeHttpOnly: true,
      });
      return cookies || {};
    } catch (error) {
      console.error("Failed to extract cookies:", error);
      return null;
    }
  }

  private async validateAndStoreState(
    state: WebContainerState
  ): Promise<boolean> {
    try {
      // Validate state data
      if (!this.validateStateData(state)) {
        return false;
      }

      // Sanitize sensitive data
      const sanitizedState = this.sanitizeStateData(state);

      // Check size limits
      const stateString = JSON.stringify(sanitizedState);
      if (stateString.length > this.config.maxStateSize) {
        console.warn("State data exceeds size limit, truncating...");
        // Could implement data compression or selective removal here
        return false;
      }

      // Create backup of current state before updating
      try {
        const currentState = await Preferences.get({
          key: WebContainerPersistenceService.STORAGE_KEY,
        });
        if (currentState.value) {
          await Preferences.set({
            key: WebContainerPersistenceService.BACKUP_KEY,
            value: currentState.value,
          });
        }
      } catch (backupError) {
        console.warn("Failed to create backup:", backupError);
        // Continue anyway - backup failure shouldn't prevent state saving
      }

      // Store the new state
      await Preferences.set({
        key: WebContainerPersistenceService.STORAGE_KEY,
        value: stateString,
      });

      return true;
    } catch (error) {
      console.error("Failed to validate and store state:", error);
      return false;
    }
  }

  private async retrieveAndValidateState(): Promise<WebContainerState | null> {
    try {
      // Try to retrieve primary state
      const result = await Preferences.get({
        key: WebContainerPersistenceService.STORAGE_KEY,
      });

      if (!result.value) {
        console.log("No primary state found");
        return null;
      }

      let state: WebContainerState;
      try {
        state = JSON.parse(result.value);
      } catch (parseError) {
        console.warn("Primary state corrupted, trying backup:", parseError);

        // Try backup state
        const backupResult = await Preferences.get({
          key: WebContainerPersistenceService.BACKUP_KEY,
        });
        if (!backupResult.value) {
          console.error("No backup state available");
          return null;
        }

        try {
          state = JSON.parse(backupResult.value);
          console.log("Using backup state");
        } catch (backupParseError) {
          console.error("Backup state also corrupted:", backupParseError);
          return null;
        }
      }

      // Validate the retrieved state
      if (!this.validateStateData(state)) {
        console.error("Retrieved state failed validation");
        return null;
      }

      return state;
    } catch (error) {
      console.error("Failed to retrieve state:", error);
      return null;
    }
  }

  private isUrlCompatible(storedUrl: string, currentUrl: string): boolean {
    try {
      const storedDomain = new URL(storedUrl).hostname;
      const currentDomain = new URL(currentUrl).hostname;

      // Allow same domain or subdomains
      return (
        storedDomain === currentDomain ||
        storedUrl.includes(currentDomain) ||
        currentUrl.includes(storedDomain)
      );
    } catch (error) {
      console.error("URL compatibility check failed:", error);
      return false;
    }
  }

  private async restoreLocalStorage(
    data: Record<string, string>
  ): Promise<void> {
    if (!data || Object.keys(data).length === 0) return;

    try {
      // Generate unique request ID for message correlation
      const requestId = `setLocalStorage_${Date.now()}_${Math.random()
        .toString(36)
        .substring(2, 11)}`;

      // Set up message listener for the response
      const responsePromise = new Promise<void>((resolve, reject) => {
        let listenerHandle: any = null;

        const timeout = setTimeout(() => {
          if (listenerHandle) {
            listenerHandle.remove();
          }
          reject(new Error("localStorage restoration timeout"));
        }, 10000); // 10 second timeout

        // Listen for response from web container
        InAppBrowser.addListener("messageFromWebview", (event) => {
          if (
            event.detail &&
            event.detail.requestId === requestId &&
            event.detail.type === "setLocalStorageResponse"
          ) {
            clearTimeout(timeout);
            if (listenerHandle) {
              listenerHandle.remove();
            }

            if (event.detail.success) {
              resolve();
            } else {
              reject(
                new Error(
                  event.detail.error || "Failed to restore localStorage"
                )
              );
            }
          }
        }).then((handle) => {
          listenerHandle = handle;
        });

        // Inject message listener script into web container if not already present
        const listenerScript = `
          (function() {
            // Only inject listener once
            if (window._setLocalStorageListenerInjected) return;
            window._setLocalStorageListenerInjected = true;

            // Listen for localStorage restoration requests from native app
            window.addEventListener('messageFromNative', function(event) {
              if (event.detail && event.detail.type === 'setLocalStorage') {
                try {
                  const data = event.detail.data || {};
                  for (const [key, value] of Object.entries(data)) {
                    try {
                      localStorage.setItem(key, value);
                    } catch (e) {
                      console.warn('Failed to restore localStorage item:', key, e);
                    }
                  }

                  // Send success response back to native app
                  window.mobileApp.postMessage({
                    detail: {
                      requestId: event.detail.requestId,
                      type: 'setLocalStorageResponse',
                      success: true
                    }
                  });
                } catch (error) {
                  // Send error response back to native app
                  window.mobileApp.postMessage({
                    detail: {
                      requestId: event.detail.requestId,
                      type: 'setLocalStorageResponse',
                      success: false,
                      error: error.message || 'Failed to restore localStorage'
                    }
                  });
                }
              }
            });
          })();
        `;

        // Inject the listener script first
        InAppBrowser.executeScript({ code: listenerScript })
          .then(() => {
            // Send the localStorage restoration request
            InAppBrowser.postMessage({
              detail: {
                requestId: requestId,
                type: "setLocalStorage",
                data: data,
              },
            });
          })
          .catch((error) => {
            clearTimeout(timeout);
            if (listenerHandle) {
              listenerHandle.remove();
            }
            reject(error);
          });
      });

      await responsePromise;
    } catch (error) {
      console.error("Failed to restore localStorage:", error);
      throw error;
    }
  }

  private async restoreCookies(data: Record<string, string>): Promise<void> {
    if (!data || Object.keys(data).length === 0) return;

    try {
      // Generate unique request ID for message correlation
      const requestId = `setCookies_${Date.now()}_${Math.random()
        .toString(36)
        .substring(2, 11)}`;

      // Prepare cookie strings in the same format as before
      const cookieStrings = Object.entries(data).map(
        ([key, value]) => `${key}=${value}; path=/; SameSite=Lax`
      );

      // Set up message listener for the response
      const responsePromise = new Promise<void>((resolve, reject) => {
        let listenerHandle: any = null;

        const timeout = setTimeout(() => {
          if (listenerHandle) {
            listenerHandle.remove();
          }
          reject(new Error("cookies restoration timeout"));
        }, 10000); // 10 second timeout

        // Listen for response from web container
        InAppBrowser.addListener("messageFromWebview", (event) => {
          if (
            event.detail &&
            event.detail.requestId === requestId &&
            event.detail.type === "setCookiesResponse"
          ) {
            clearTimeout(timeout);
            if (listenerHandle) {
              listenerHandle.remove();
            }

            if (event.detail.success) {
              resolve();
            } else {
              reject(
                new Error(event.detail.error || "Failed to restore cookies")
              );
            }
          }
        }).then((handle) => {
          listenerHandle = handle;
        });

        // Inject message listener script into web container if not already present
        const listenerScript = `
          (function() {
            // Only inject listener once
            if (window._setCookiesListenerInjected) return;
            window._setCookiesListenerInjected = true;

            // Listen for cookies restoration requests from native app
            window.addEventListener('messageFromNative', function(event) {
              if (event.detail && event.detail.type === 'setCookies') {
                try {
                  const cookieStrings = event.detail.data || [];
                  cookieStrings.forEach(cookie => {
                    try {
                      document.cookie = cookie;
                    } catch (e) {
                      console.warn('Failed to restore cookie:', cookie, e);
                    }
                  });

                  // Send success response back to native app
                  window.mobileApp.postMessage({
                    detail: {
                      requestId: event.detail.requestId,
                      type: 'setCookiesResponse',
                      success: true
                    }
                  });
                } catch (error) {
                  // Send error response back to native app
                  window.mobileApp.postMessage({
                    detail: {
                      requestId: event.detail.requestId,
                      type: 'setCookiesResponse',
                      success: false,
                      error: error.message || 'Failed to restore cookies'
                    }
                  });
                }
              }
            });
          })();
        `;

        // Inject the listener script first
        InAppBrowser.executeScript({ code: listenerScript })
          .then(() => {
            // Send the cookies restoration request
            InAppBrowser.postMessage({
              detail: {
                requestId: requestId,
                type: "setCookies",
                data: cookieStrings,
              },
            });
          })
          .catch((error) => {
            clearTimeout(timeout);
            if (listenerHandle) {
              listenerHandle.remove();
            }
            reject(error);
          });
      });

      await responsePromise;
    } catch (error) {
      console.error("Failed to restore cookies:", error);
      throw error;
    }
  }

  // Validation and sanitization utilities
  private validateStateData(state: any): state is WebContainerState {
    try {
      // Check required fields
      if (!state.version || !state.timestamp || !state.url) {
        console.error("State missing required fields");
        return false;
      }

      // Check version compatibility
      if (state.version !== WebContainerPersistenceService.CURRENT_VERSION) {
        console.warn("State version mismatch:", state.version);
        // Could implement migration logic here
      }

      // Check age
      const age = Date.now() - state.timestamp;
      if (age > this.config.maxStateAge) {
        console.error("State data too old:", age);
        return false;
      }

      // Check URL validity
      try {
        new URL(state.url);
      } catch (urlError) {
        console.error("Invalid URL in state:", state.url);
        return false;
      }

      // Check data structure
      if (
        typeof state.localStorage !== "object" ||
        typeof state.cookies !== "object"
      ) {
        console.error("State data structure invalid");
        return false;
      }

      return true;
    } catch (error) {
      console.error("State validation error:", error);
      return false;
    }
  }

  private sanitizeStateData(state: WebContainerState): WebContainerState {
    const sanitized = { ...state };

    // Sanitize localStorage
    sanitized.localStorage = this.sanitizeKeyValueData(state.localStorage);

    // Sanitize cookies
    sanitized.cookies = this.sanitizeKeyValueData(state.cookies);

    return sanitized;
  }

  private sanitizeKeyValueData(
    data: Record<string, string>
  ): Record<string, string> {
    const sanitized: Record<string, string> = {};

    for (const [key, value] of Object.entries(data)) {
      if (
        !this.containsSensitiveData(key) &&
        !this.containsSensitiveData(value)
      ) {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  private containsSensitiveData(text: string): boolean {
    const lowerText = text.toLowerCase();
    return this.config.excludePatterns.some((pattern) =>
      lowerText.includes(pattern.toLowerCase())
    );
  }
}
