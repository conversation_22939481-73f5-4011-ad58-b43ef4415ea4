<template>
  <ion-page>
    <!-- Loading state -->
    <div v-if="isLoading || isRestoringState" class="loading-container">
      <ion-spinner name="crescent"></ion-spinner>
      <p v-if="isRestoringState">Sitzung wird wiederhergestellt...</p>
      <p v-else>Laden...</p>
    </div>

    <!-- Fehlerzustand bei Netzwerkproblemen -->
    <div v-else-if="showNetworkError" class="error-container">
      <ion-icon :icon="wifiOutline" class="error-icon"></ion-icon>
      <h2>Keine Internetverbindung verfügbar</h2>
      <ion-button @click="retryConnection" expand="block" class="retry-button">
        Erneut versuchen
      </ion-button>
    </div>

    <!-- Fehlerzustand bei ungültiger URL -->
    <div v-else-if="showUrlError" class="error-container">
      <ion-icon :icon="alertCircleOutline" class="error-icon"></ion-icon>
      <h2>Ungültige URL-Konfiguration</h2>
      <p>{{ urlErrorMessage }}</p>
    </div>

    <!-- Web container will be opened programmatically -->
    <!-- No visible content needed as in-app browser takes full screen -->
  </ion-page>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { PrivacyScreen } from "@capacitor-community/privacy-screen";
import {
  IonPage,
  IonSpinner,
  IonButton,
  IonIcon,
  alertController,
} from "@ionic/vue";
import { wifiOutline, alertCircleOutline } from "ionicons/icons";
import {
  InAppBrowser,
  ToolBarType,
  BackgroundColor,
} from "@capgo/inappbrowser";
import { Network } from "@capacitor/network";
import { App } from "@capacitor/app";
import type { ConnectionStatus } from "@capacitor/network";
import { WebContainerPersistenceService } from "../services/web-container-persistence-service";
import { DebugToolbarService } from "../services/debug-toolbar-service";

// Reactive state
const isLoading = ref(true);
const showNetworkError = ref(false);
const showUrlError = ref(false);
const urlErrorMessage = ref("");
const networkStatus = ref<ConnectionStatus | null>(null);
const retryInterval = ref<NodeJS.Timeout | null>(null);
const isRestoringState = ref(false);
const currentUrl = ref<string>("");
const previousUrl = ref<string>("");

// Configuration
const targetUrl = import.meta.env.VITE_TARGET_URL || "https://google.com";
const appName = import.meta.env.VITE_APP_NAME || "Web2App";
const enableScreenshot = import.meta.env.VITE_ENABLE_SCREENSHOTS || "true";

// Services
const persistenceService = new WebContainerPersistenceService();
const debugToolbarService = new DebugToolbarService();

// URL validation function
const isValidUrl = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    return urlObj.protocol === "https:" || urlObj.protocol === "http:";
  } catch {
    return false;
  }
};

// Domain comparison utility function
const isDomainChange = (previousUrl: string, currentUrl: string): boolean => {
  try {
    if (!previousUrl || !currentUrl) {
      return false;
    }

    const prevUrlObj = new URL(previousUrl);
    const currUrlObj = new URL(currentUrl);

    // Compare hostnames to detect domain changes
    return prevUrlObj.hostname !== currUrlObj.hostname;
  } catch (error) {
    console.warn("Error comparing domains:", error);
    return false;
  }
};

// Network status monitoring
const checkNetworkStatus = async (): Promise<boolean> => {
  try {
    const status = await Network.getStatus();
    networkStatus.value = status;
    return status.connected;
  } catch (error) {
    console.error("Failed to check network status:", error);
    return false;
  }
};

// Open web container
const openWebContainer = async () => {
  try {
    // Validate URL first
    if (!isValidUrl(targetUrl)) {
      showUrlError.value = true;
      urlErrorMessage.value = `Invalid URL: ${targetUrl}. Please ensure the URL includes a valid protocol (https:// or http://).`;
      isLoading.value = false;
      return;
    }

    // Check network connectivity
    const isConnected = await checkNetworkStatus();
    if (!isConnected) {
      showNetworkError.value = true;
      isLoading.value = false;
      startRetryInterval();
      return;
    }

    // Open the web container with full-screen configuration
    await InAppBrowser.openWebView({
      url: targetUrl,
      title: appName,
      toolbarType: ToolBarType.BLANK, // No toolbar for full-screen experience
      isPresentAfterPageLoad: true, // Present immediately for 500ms requirement
      isAnimated: false, // Disable animation for faster loading
      backgroundColor: BackgroundColor.WHITE,
      visibleTitle: false, // Hide title for full-screen experience
      showReloadButton: false, // No reload button for clean UI
      closeModal: false, // No close confirmation for seamless experience
      ignoreUntrustedSSLError: false, // Maintain security
      preventDeeplink: false, // Allow deep links to work
      activeNativeNavigationForWebview: true, // Enable native navigation
      disableGoBackOnNativeApplication: true, // Prevent going back to app
    });

    // Set current URL for persistence
    currentUrl.value = targetUrl;
    previousUrl.value = ""; // Initialize as empty for first load

    // Set up URL change listener for persistence tracking
    await setupUrlChangeListener();

    // Restore state after web container is ready
    await restoreWebContainerState();

    //Page Refresh
    await InAppBrowser.reload();

    // Inject debug toolbar if enabled
    if (debugToolbarService.isEnabled()) {
      try {
        await debugToolbarService.injectToolbar();
        console.log("Debug toolbar status:", debugToolbarService.getStatus());
      } catch (error) {
        console.warn("Failed to inject debug toolbar:", error);
        // Don't fail the entire container loading if toolbar injection fails
      }
    }

    isLoading.value = false;
    console.log("Web container opened successfully");
  } catch (error) {
    console.error("Failed to open web container:", error);
    await showErrorAlert(
      "Failed to load website",
      "An error occurred while trying to load the website. Please try again."
    );
    isLoading.value = false;
  }
};

// Retry connection mechanism
const retryConnection = async () => {
  isLoading.value = true;
  showNetworkError.value = false;
  clearRetryInterval();

  // Small delay to show loading state
  setTimeout(async () => {
    await openWebContainer();
  }, 500);
};

// Start automatic retry interval (30 seconds)
const startRetryInterval = () => {
  clearRetryInterval();
  retryInterval.value = setInterval(async () => {
    const isConnected = await checkNetworkStatus();
    if (isConnected) {
      retryConnection();
    }
  }, 30000); // 30 seconds
};

// Clear retry interval
const clearRetryInterval = () => {
  if (retryInterval.value) {
    clearInterval(retryInterval.value);
    retryInterval.value = null;
  }
};

// Show error alert
const showErrorAlert = async (header: string, message: string) => {
  const alert = await alertController.create({
    header,
    message,
    buttons: ["OK"],
  });
  await alert.present();
};

// Handle debug toolbar re-injection after URL changes
const handleToolbarReinjection = async () => {
  try {
    // Small delay to allow page to load
    setTimeout(async () => {
      try {
        // Check if toolbar is still present
        const isPresent = await debugToolbarService.isToolbarPresent();

        if (!isPresent) {
          console.log(
            "Debug toolbar not found after navigation, re-injecting..."
          );
          const success = await debugToolbarService.reinjectToolbar();

          if (success) {
            console.log(
              "Debug toolbar re-injected successfully after navigation"
            );
          } else {
            console.warn("Failed to re-inject debug toolbar after navigation");
          }
        } else {
          console.log("Debug toolbar still present after navigation");
        }
      } catch (error) {
        console.error("Error during toolbar re-injection:", error);
      }
    }, 1000); // 1 second delay to allow page to stabilize
  } catch (error) {
    console.error("Failed to handle toolbar re-injection:", error);
  }
};

// Setup URL change listener for persistence tracking and toolbar re-injection
const setupUrlChangeListener = async () => {
  try {
    await InAppBrowser.addListener("urlChangeEvent", async (event) => {
      if (event && event.url) {
        console.log("URL changed to:", event.url);

        // Check for domain change and save state if needed
        const domainChanged = isDomainChange(previousUrl.value, event.url);
        if (domainChanged && previousUrl.value) {
          console.log(
            "Domain change detected:",
            previousUrl.value,
            "->",
            event.url
          );
          // Save state before domain change
          await captureWebContainerState("Domain Change");
        }

        // Update URL tracking
        previousUrl.value = currentUrl.value;
        currentUrl.value = event.url;

        // Handle debug toolbar re-injection if enabled
        if (debugToolbarService.isEnabled()) {
          await handleToolbarReinjection();
        }

        // Load state for new domain if available
        if (domainChanged) {
          console.log("Loading state for new domain:", event.url);
          await restoreWebContainerState();
        }
      }
    });
  } catch (error) {
    console.error("Failed to setup URL change listener:", error);
  }
};

// Restore web container state
const restoreWebContainerState = async () => {
  if (!persistenceService.isEnabled()) {
    console.log("Persistence is disabled, skipping state restoration");
    return;
  }

  try {
    isRestoringState.value = true;
    console.log("Attempting to restore web container state...");

    // Wait a moment for web container to be fully ready
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const success = await persistenceService.restoreState(currentUrl.value);

    if (success) {
      console.log("State restoration completed successfully");
    } else {
      console.log("No state to restore or restoration failed");
    }
  } catch (error) {
    console.error("State restoration error:", error);
    // Don't show error to user - restoration failure shouldn't break app
  } finally {
    isRestoringState.value = false;
  }
};

// Capture web container state
const captureWebContainerState = async (reason?: string) => {
  if (!persistenceService.isEnabled() || !currentUrl.value) {
    return;
  }

  try {
    const logPrefix = reason ? `[${reason}] ` : "";
    console.log(
      `${logPrefix}Capturing web container state for:`,
      currentUrl.value
    );
    const success = await persistenceService.captureState(currentUrl.value);

    if (success) {
      console.log(`${logPrefix}State captured successfully`);
    } else {
      console.log(`${logPrefix}State capture failed or was skipped`);
    }
  } catch (error) {
    console.error("State capture error:", error);
    // Don't show error to user - capture failure shouldn't break app
  }
};

// Network status change listener
const setupNetworkListener = async () => {
  await Network.addListener("networkStatusChange", async (status) => {
    networkStatus.value = status;

    if (status.connected && showNetworkError.value) {
      // Connection restored, retry immediately
      retryConnection();
    } else if (
      !status.connected &&
      !showNetworkError.value &&
      !isLoading.value
    ) {
      // Connection lost, show error
      showNetworkError.value = true;
      InAppBrowser.close();
      startRetryInterval();
    }
  });
};

// Setup app lifecycle listeners for persistence
const setupAppLifecycleListeners = async () => {
  try {
    // Listen for app state changes (background/foreground)
    await App.addListener("appStateChange", async ({ isActive }) => {
      if (!isActive) {
        // App going to background - capture state
        if (showNetworkError.value === false) {
          await captureWebContainerState("App Background");
        }
      } else {
        // App returning to foreground - restore state if needed
        if (currentUrl.value && persistenceService.isEnabled()) {
          // would work, just commented out so it doesn't restore state when app is brought to foreground
          // breaks microsoft sso
          // await restoreWebContainerState();
        }
      }
    });

    // Listen for app resume events
    await App.addListener("resume", async () => {
      if (currentUrl.value && persistenceService.isEnabled()) {
        await restoreWebContainerState();
      }
    });

    console.log("App lifecycle listeners setup successfully");
  } catch (error) {
    console.error("Failed to setup app lifecycle listeners:", error);
  }
};

// Component lifecycle
onMounted(async () => {
  if (enableScreenshot === "false") {
    await PrivacyScreen.enable();
  }
  try {
    // Initialize persistence service
    await persistenceService.initialize();
    console.log("Persistence Status:", persistenceService.getStatus());

    // Set up network monitoring
    await setupNetworkListener();

    // Set up app lifecycle listeners for persistence
    await setupAppLifecycleListeners();

    // Start the web container (with minimal delay to ensure component is ready)
    setTimeout(async () => {
      await openWebContainer();
    }, 100);
  } catch (error) {
    console.error("Failed to initialize component:", error);
    await showErrorAlert(
      "Initialization Error",
      "Failed to initialize the application. Please restart the app."
    );
  }
});

onUnmounted(async () => {
  if (enableScreenshot === "false") {
    await PrivacyScreen.disable();
  }
  try {
    // Capture state before component unmounts
    if (showNetworkError.value === false) {
      await captureWebContainerState("Component Unmount");
    }
    // Clean up debug toolbar
    if (debugToolbarService.isEnabled()) {
      await debugToolbarService.removeToolbar();
    }

    // Clean up intervals and listeners
    clearRetryInterval();
    Network.removeAllListeners();
    App.removeAllListeners();
    InAppBrowser.removeAllListeners();

    console.log("Component cleanup completed");
  } catch (error) {
    console.error("Error during component cleanup:", error);
  }
});
</script>

<style scoped>
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
  padding: 20px;
}

.error-icon {
  font-size: 64px;
  color: #666;
  margin-bottom: 16px;
}

.error-container h2 {
  margin: 16px 0;
  color: #333;
}

.error-container p {
  margin: 16px 0;
  color: #666;
  line-height: 1.5;
}

.retry-button {
  margin-top: 24px;
  min-height: 44px;
  width: 200px;
}
</style>
