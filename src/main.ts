import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import { SplashScreen } from "@capacitor/splash-screen";
import { pushNotificationService } from "./services/push-notification-service";
import { App as CapacitorApp } from "@capacitor/app";

import { IonicVue } from "@ionic/vue";

/* Core CSS required for Ionic components to work properly */
import "@ionic/vue/css/core.css";

/* Basic CSS for apps built with Ionic */
import "@ionic/vue/css/normalize.css";
import "@ionic/vue/css/structure.css";
import "@ionic/vue/css/typography.css";

/* Optional CSS utils that can be commented out */
import "@ionic/vue/css/padding.css";
import "@ionic/vue/css/float-elements.css";
import "@ionic/vue/css/text-alignment.css";
import "@ionic/vue/css/text-transformation.css";
import "@ionic/vue/css/flex-utils.css";
import "@ionic/vue/css/display.css";

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import '@ionic/vue/css/palettes/dark.always.css'; */
/* @import '@ionic/vue/css/palettes/dark.class.css'; */
import "@ionic/vue/css/palettes/dark.system.css";

/* Theme variables */
import "./theme/variables.css";

const app = createApp(App).use(IonicVue).use(router);

router.isReady().then(async () => {
  await SplashScreen.show({
    autoHide: false,
  });
  app.mount("#app");

  // Initialize push notifications
  if (pushNotificationService.isSupported()) {
    try {
      await pushNotificationService.initialize();
      console.log('Push notification service initialized');

      // Process any pending background data
      await pushNotificationService.processBackgroundData();
      console.log('Background data processed');
    } catch (error) {
      console.error('Failed to initialize push notifications:', error);
    }
  }

  // Set up app state change listeners for background processing
  CapacitorApp.addListener('appStateChange', async ({ isActive }) => {
    if (isActive && pushNotificationService.isSupported()) {
      try {
        // Process background data when app becomes active
        await pushNotificationService.processBackgroundData();
        console.log('Background data processed on app activation');
      } catch (error) {
        console.error('Failed to process background data on activation:', error);
      }
    }
  });
});
