/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_TARGET_URL: string
  readonly VITE_APP_NAME: string
  readonly VITE_ENABLE_SCREENSHOTS: string
  readonly VITE_PERSISTENCE_ENABLED: string
  readonly VITE_PERSISTENCE_MAX_AGE_DAYS: string
  readonly VITE_PERSISTENCE_MAX_SIZE_MB: string
  readonly VITE_PERSISTENCE_EXCLUDE_PATTERNS: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// Global build-time constants
declare const __APP_NAME__: string
declare const __TARGET_URL__: string
