package de.sva.web2app;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import androidx.annotation.NonNull;

import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.messaging.FirebaseMessaging;

/**
 * Manager class for handling FCM token operations
 * Handles token retrieval, storage, and server communication
 */
public class FCMTokenManager {

    private static final String TAG = "FCMTokenManager";
    private static final String PREFS_NAME = "fcm_prefs";
    private static final String KEY_FCM_TOKEN = "fcm_token";
    private static final String KEY_TOKEN_SENT_TO_SERVER = "token_sent_to_server";
    private static final String KEY_TOKEN_TIMESTAMP = "token_timestamp";

    private final Context context;
    private final SharedPreferences sharedPreferences;

    public FCMTokenManager(Context context) {
        this.context = context;
        this.sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }

    /**
     * Interface for token retrieval callbacks
     */
    public interface TokenCallback {
        void onTokenReceived(String token);
        void onTokenError(Exception exception);
    }

    /**
     * Interface for token operations callbacks
     */
    public interface TokenOperationCallback {
        void onSuccess();
        void onError(Exception exception);
    }

    /**
     * Get the current FCM token
     */
    public void getCurrentToken(TokenCallback callback) {
        FirebaseMessaging.getInstance().getToken()
                .addOnCompleteListener(new OnCompleteListener<String>() {
                    @Override
                    public void onComplete(@NonNull Task<String> task) {
                        if (!task.isSuccessful()) {
                            Log.w(TAG, "Fetching FCM registration token failed", task.getException());
                            if (callback != null) {
                                callback.onTokenError(task.getException());
                            }
                            return;
                        }

                        // Get new FCM registration token
                        String token = task.getResult();
                        Log.d(TAG, "FCM Registration Token: " + token);

                        // Save token locally
                        saveTokenLocally(token);

                        // Notify callback
                        if (callback != null) {
                            callback.onTokenReceived(token);
                        }

                        // Send token to server if not already sent
                        if (!isTokenSentToServer() || !token.equals(getStoredToken())) {
                            sendTokenToServer(token, null);
                        }
                    }
                });
    }

    /**
     * Get the stored token from local storage
     */
    public String getStoredToken() {
        return sharedPreferences.getString(KEY_FCM_TOKEN, null);
    }

    /**
     * Save token to local storage
     */
    public void saveTokenLocally(String token) {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putString(KEY_FCM_TOKEN, token);
        editor.putLong(KEY_TOKEN_TIMESTAMP, System.currentTimeMillis());
        editor.apply();
        Log.d(TAG, "Token saved locally");
    }

    /**
     * Check if token has been sent to server
     */
    public boolean isTokenSentToServer() {
        return sharedPreferences.getBoolean(KEY_TOKEN_SENT_TO_SERVER, false);
    }

    /**
     * Mark token as sent to server
     */
    public void markTokenAsSentToServer() {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putBoolean(KEY_TOKEN_SENT_TO_SERVER, true);
        editor.apply();
        Log.d(TAG, "Token marked as sent to server");
    }

    /**
     * Send token to your app server
     */
    public void sendTokenToServer(String token, TokenOperationCallback callback) {
        Log.d(TAG, "Sending token to server: " + token);

        // TODO: Implement actual server communication
        // This is where you would send the token to your backend server
        // Example:
        // ApiService.sendTokenToServer(token)
        //     .enqueue(new Callback<ResponseBody>() {
        //         @Override
        //         public void onResponse(Call<ResponseBody> call, Response<ResponseBody> response) {
        //             if (response.isSuccessful()) {
        //                 markTokenAsSentToServer();
        //                 if (callback != null) callback.onSuccess();
        //             } else {
        //                 if (callback != null) callback.onError(new Exception("Server error"));
        //             }
        //         }
        //
        //         @Override
        //         public void onFailure(Call<ResponseBody> call, Throwable t) {
        //             if (callback != null) callback.onError(new Exception(t));
        //         }
        //     });

        // For now, just simulate success and mark as sent
        try {
            // Simulate network delay
            new Thread(() -> {
                try {
                    Thread.sleep(1000);
                    markTokenAsSentToServer();
                    Log.i(TAG, "Token successfully sent to server (simulated)");
                    if (callback != null) {
                        callback.onSuccess();
                    }
                } catch (InterruptedException e) {
                    Log.e(TAG, "Error simulating server call", e);
                    if (callback != null) {
                        callback.onError(e);
                    }
                }
            }).start();
        } catch (Exception e) {
            Log.e(TAG, "Error sending token to server", e);
            if (callback != null) {
                callback.onError(e);
            }
        }
    }

    /**
     * Handle token refresh
     */
    public void onTokenRefresh(String newToken) {
        Log.d(TAG, "Token refreshed: " + newToken);
        
        // Save new token
        saveTokenLocally(newToken);
        
        // Mark as not sent to server since it's a new token
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putBoolean(KEY_TOKEN_SENT_TO_SERVER, false);
        editor.apply();
        
        // Send new token to server
        sendTokenToServer(newToken, new TokenOperationCallback() {
            @Override
            public void onSuccess() {
                Log.d(TAG, "Refreshed token successfully sent to server");
            }

            @Override
            public void onError(Exception exception) {
                Log.e(TAG, "Failed to send refreshed token to server", exception);
            }
        });
    }

    /**
     * Delete token from local storage
     */
    public void deleteToken() {
        // Delete token from Firebase
        FirebaseMessaging.getInstance().deleteToken()
                .addOnCompleteListener(new OnCompleteListener<Void>() {
                    @Override
                    public void onComplete(@NonNull Task<Void> task) {
                        if (task.isSuccessful()) {
                            Log.d(TAG, "FCM token deleted successfully");
                            
                            // Clear local storage
                            SharedPreferences.Editor editor = sharedPreferences.edit();
                            editor.remove(KEY_FCM_TOKEN);
                            editor.remove(KEY_TOKEN_SENT_TO_SERVER);
                            editor.remove(KEY_TOKEN_TIMESTAMP);
                            editor.apply();
                            
                        } else {
                            Log.e(TAG, "Failed to delete FCM token", task.getException());
                        }
                    }
                });
    }

    /**
     * Get token age in milliseconds
     */
    public long getTokenAge() {
        long timestamp = sharedPreferences.getLong(KEY_TOKEN_TIMESTAMP, 0);
        if (timestamp == 0) {
            return -1; // No token stored
        }
        return System.currentTimeMillis() - timestamp;
    }

    /**
     * Check if token needs refresh (older than 24 hours)
     */
    public boolean shouldRefreshToken() {
        long age = getTokenAge();
        return age == -1 || age > 24 * 60 * 60 * 1000; // 24 hours
    }

    /**
     * Initialize token management
     * Call this when the app starts
     */
    public void initialize() {
        Log.d(TAG, "Initializing FCM Token Manager");
        
        // Get current token
        getCurrentToken(new TokenCallback() {
            @Override
            public void onTokenReceived(String token) {
                Log.i(TAG, "FCM Token initialized: " + token);
            }

            @Override
            public void onTokenError(Exception exception) {
                Log.e(TAG, "Failed to initialize FCM token", exception);
            }
        });
    }

    /**
     * Subscribe to a topic
     */
    public void subscribeToTopic(String topic, TokenOperationCallback callback) {
        FirebaseMessaging.getInstance().subscribeToTopic(topic)
                .addOnCompleteListener(new OnCompleteListener<Void>() {
                    @Override
                    public void onComplete(@NonNull Task<Void> task) {
                        if (task.isSuccessful()) {
                            Log.d(TAG, "Subscribed to topic: " + topic);
                            if (callback != null) callback.onSuccess();
                        } else {
                            Log.e(TAG, "Failed to subscribe to topic: " + topic, task.getException());
                            if (callback != null) callback.onError(task.getException());
                        }
                    }
                });
    }

    /**
     * Unsubscribe from a topic
     */
    public void unsubscribeFromTopic(String topic, TokenOperationCallback callback) {
        FirebaseMessaging.getInstance().unsubscribeFromTopic(topic)
                .addOnCompleteListener(new OnCompleteListener<Void>() {
                    @Override
                    public void onComplete(@NonNull Task<Void> task) {
                        if (task.isSuccessful()) {
                            Log.d(TAG, "Unsubscribed from topic: " + topic);
                            if (callback != null) callback.onSuccess();
                        } else {
                            Log.e(TAG, "Failed to unsubscribe from topic: " + topic, task.getException());
                            if (callback != null) callback.onError(task.getException());
                        }
                    }
                });
    }
}
