import type { CapacitorConfig } from '@capacitor/cli';
import { config as dotenvConfig } from 'dotenv';

// Load environment variables
dotenvConfig();

// Generate app ID from app name (sanitize for bundle identifier)
const appName = process.env.VITE_APP_NAME || 'Web2App';
const sanitizedAppName = appName.toLowerCase().replace(/[^a-z0-9]/g, '');
const appId = `de.sva.web2app.${sanitizedAppName}`;

const config: CapacitorConfig = {
  appId: appId,
  appName: appName,
  webDir: 'dist',
  plugins: {
    InAppBrowser: {
      // Plugin is automatically registered by Capacitor
    },
    Network: {
      // Plugin is automatically registered by Capacitor
    },
    PushNotifications: {
      presentationOptions: ["badge", "sound", "alert"]
    }
  }
};

export default config;
