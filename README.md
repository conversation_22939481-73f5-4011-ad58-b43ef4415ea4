# SVA Web2App

A sophisticated cross-platform web container application built with Ionic Vue and Capacitor, designed to wrap web applications into native mobile experiences with advanced state persistence and debugging capabilities.

## Overview

SVA Web2App transforms web applications into native mobile apps by providing a robust web container with enterprise-grade features. The application leverages the power of Ionic Framework and Capacitor to deliver native performance while maintaining web application flexibility.

### Key Features

- **🌐 Web Container**: Advanced in-app browser using @capgo/inappbrowser for seamless web application integration
- **💾 State Persistence**: Intelligent state management with localStorage and cookie persistence across app sessions
- **🔧 Debug Toolbar**: Comprehensive debugging tools for development and troubleshooting
- **📱 Cross-Platform**: Native iOS and Android apps from a single codebase
- **🔄 Network Monitoring**: Real-time network status monitoring with automatic retry mechanisms
- **⚙️ Environment Configuration**: Flexible configuration through environment variables
- **🎨 Modern UI**: Clean, responsive interface with Ionic components and dark mode support

### Architecture

Built on a modern technology stack:
- **Frontend**: Vue 3 + TypeScript + Ionic Framework
- **Mobile**: Capacitor for native platform integration
- **Build System**: Vite for fast development and optimized builds
- **Testing**: Vitest for unit tests, Cypress for E2E testing
- **Code Quality**: ESLint + TypeScript for code consistency

## Features in Detail

### Web Container Functionality

The application provides a sophisticated web container experience:

- **Advanced In-App Browser**: Utilizes @capgo/inappbrowser for superior web content rendering
- **Customizable Toolbar**: Configurable browser toolbar with navigation controls
- **URL Validation**: Robust URL handling with error detection and user feedback
- **Loading States**: Smooth loading indicators and error handling
- **Background Color**: Adaptive background colors for seamless user experience

### State Persistence System

Enterprise-grade state management across app sessions:

- **localStorage Persistence**: Automatic capture and restoration of web application localStorage
- **Cookie Management**: Intelligent cookie handling with domain-specific storage
- **State Validation**: Data integrity checks with automatic cleanup of corrupted states
- **Configurable Retention**: Customizable state retention periods and size limits
- **Sensitive Data Protection**: Pattern-based exclusion of sensitive information
- **Background/Foreground Sync**: Automatic state capture during app lifecycle events

### Debug Toolbar

Comprehensive debugging capabilities for development and troubleshooting:

- **Real-time State Inspection**: Live viewing of localStorage and cookie data
- **Interactive Controls**: Buttons for state capture, restoration, and clearing
- **Network Status Display**: Visual network connectivity indicators
- **Draggable Interface**: Repositionable toolbar for optimal development workflow
- **Persistence Integration**: Direct integration with state persistence service
- **Environment-based Activation**: Configurable via environment variables

### Cross-Platform Support

Native mobile app generation for multiple platforms:

- **iOS Support**: Full iOS app with native capabilities and App Store compatibility
- **Android Support**: Complete Android app with Google Play Store readiness
- **Shared Codebase**: Single Vue.js codebase for all platforms
- **Platform-specific Optimizations**: Tailored user experience for each platform
- **Native Plugin Integration**: Access to device features through Capacitor plugins

### Network Monitoring

Intelligent network handling and user feedback:

- **Real-time Status**: Continuous monitoring of network connectivity
- **Automatic Retry**: Smart retry mechanisms for failed connections
- **User Notifications**: Clear feedback for network-related issues
- **Offline Handling**: Graceful degradation when network is unavailable
- **Connection Type Detection**: Awareness of WiFi vs cellular connections

## Technical Architecture

### Application Stack

```
┌─────────────────────────────────────┐
│           Mobile App Layer          │
│  (iOS/Android Native Containers)    │
├─────────────────────────────────────┤
│         Capacitor Bridge            │
│    (Native Plugin Integration)      │
├─────────────────────────────────────┤
│          Ionic Framework            │
│     (UI Components & Styling)       │
├─────────────────────────────────────┤
│            Vue 3 Layer              │
│   (Reactive Components & Router)    │
├─────────────────────────────────────┤
│         Application Services        │
│  (Persistence, Debug, Networking)   │
├─────────────────────────────────────┤
│         Web Container Core          │
│      (@capgo/inappbrowser)          │
└─────────────────────────────────────┘
```

### Core Services

#### WebContainerPersistenceService
- **Purpose**: Manages state persistence across app sessions
- **Features**: localStorage/cookie capture, data validation, configurable retention
- **Integration**: Capacitor Preferences API for native storage
- **Configuration**: Environment-based settings for persistence behavior

#### DebugToolbarService
- **Purpose**: Provides debugging capabilities during development
- **Features**: State inspection, interactive controls, draggable UI
- **Integration**: PostMessage communication with web container
- **Activation**: Environment variable controlled (VITE_ENABLE_DEBUG_TOOLBAR)

#### Network Monitoring
- **Purpose**: Handles connectivity awareness and retry logic
- **Features**: Real-time status, automatic reconnection, user feedback
- **Integration**: Capacitor Network API for native network detection
- **Behavior**: Graceful degradation and recovery mechanisms

### Build System Architecture

- **Development Server**: Vite with HMR for rapid development
- **TypeScript Compilation**: Full type checking with vue-tsc
- **Asset Processing**: Optimized bundling with code splitting
- **Platform Sync**: Capacitor CLI for native project synchronization
- **Testing Pipeline**: Vitest (unit) + Cypress (E2E) integration

## Getting Started

### Prerequisites

Before setting up the project, ensure you have the following installed:

- **Node.js** (v18 or higher) - [Download](https://nodejs.org/)
- **npm** (comes with Node.js) or **yarn**
- **Git** for version control
- **Ionic CLI**: `npm install -g @ionic/cli`
- **Capacitor CLI**: `npm install -g @capacitor/cli`

#### Platform-Specific Requirements

**For iOS Development:**
- **macOS** (required for iOS development)
- **Xcode** (latest version from App Store)
- **iOS Simulator** (included with Xcode)
- **CocoaPods**: `sudo gem install cocoapods`

**For Android Development:**
- **Android Studio** with Android SDK
- **Java Development Kit (JDK)** 11 or higher
- **Android SDK Tools** and **Platform Tools**
- **Android Emulator** or physical device

### Installation

1. **Clone the Repository**
   ```bash
   git clone <repository-url>
   cd App
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**

   Create environment files based on your needs:
   ```bash
   # Create .env.local for local development
   touch .env.local
   ```

   Add your configuration (see Configuration section for details):
   ```env
   VITE_APP_NAME=SVA Web2App
   VITE_TARGET_URL=https://sva.de
   VITE_PERSISTENCE_ENABLED=true
   VITE_ENABLE_DEBUG_TOOLBAR=false
   ```

4. **Initial Build**
   ```bash
   npm run build
   ```

5. **Platform Setup**

   **For iOS:**
   ```bash
   npx cap add ios
   npx cap sync ios
   cd ios/App && pod install && cd ../..
   ```

   **For Android:**
   ```bash
   npx cap add android
   npx cap sync android
   ```

### Development Workflow

1. **Start Development Server**
   ```bash
   npm run dev
   ```
   This starts the Vite development server with hot module replacement.

2. **Run in Browser**
   ```bash
   npm run ionic:serve
   ```
   Opens the application in your default browser for web-based development.

3. **Run on Mobile Platforms**

   **iOS:**
   ```bash
   npm run build:ios
   # This builds the app and opens Xcode
   ```

   **Android:**
   ```bash
   npm run build:android
   # This builds the app and opens Android Studio
   ```

4. **Live Reload on Device**
   ```bash
   # Start dev server
   npm run dev

   # In another terminal, sync with live reload
   npx cap run ios --livereload --external
   # or
   npx cap run android --livereload --external
   ```

## Configuration

### Environment Variables

The application uses environment variables for configuration. Create `.env.local` for local development or set these in your deployment environment:

#### Core Application Settings

```env
# Application Identity
VITE_APP_NAME=SVA Web2App
# The display name for your application (used in app title and native app name)

VITE_TARGET_URL=https://sva.de
# The URL of the web application to be wrapped in the container
```

#### State Persistence Configuration

```env
# Enable/disable state persistence
VITE_PERSISTENCE_ENABLED=true

# Maximum age for stored states (in days)
VITE_PERSISTENCE_MAX_AGE_DAYS=30

# Maximum size for stored states (in MB)
VITE_PERSISTENCE_MAX_SIZE_MB=5

# Comma-separated regex patterns for excluding sensitive data
VITE_PERSISTENCE_EXCLUDE_PATTERNS=password,token,secret,key
```

#### Debug and Development Settings

```env
# Enable debug toolbar (development only)
VITE_ENABLE_DEBUG_TOOLBAR=true
```

#### Platform-Specific Settings

**iOS Configuration** (`ios/App/App/Info.plist`):
- Bundle identifier matches Capacitor app ID
- Display name from VITE_APP_NAME
- URL schemes for deep linking

**Android Configuration** (`android/app/src/main/AndroidManifest.xml`):
- Package name matches Capacitor app ID
- App label from VITE_APP_NAME
- Intent filters for URL handling

## Advanced Features

### State Persistence Service

The `WebContainerPersistenceService` provides enterprise-grade state management for web container applications, ensuring user data and application state persist across app sessions.

#### Key Capabilities

- **Automatic State Capture**: Captures localStorage and cookies during app lifecycle events
- **Intelligent Restoration**: Restores state when the web container loads
- **Data Validation**: Ensures state integrity with version checking and corruption detection
- **Configurable Retention**: Customizable storage duration and size limits
- **Sensitive Data Protection**: Pattern-based exclusion of sensitive information
- **Background Processing**: Non-blocking state operations with debouncing

#### Configuration

State persistence is controlled through environment variables:

```env
# Enable/disable persistence
VITE_PERSISTENCE_ENABLED=true

# Retention settings
VITE_PERSISTENCE_MAX_AGE_DAYS=30        # Days to keep state
VITE_PERSISTENCE_MAX_SIZE_MB=5          # Maximum storage size

# Security settings
VITE_PERSISTENCE_EXCLUDE_PATTERNS=password,token,secret,sessionId
```

#### Usage Examples

**Basic Integration:**
```typescript
import { WebContainerPersistenceService } from '@/services/WebContainerPersistenceService';

const persistenceService = new WebContainerPersistenceService();

// Initialize the service
await persistenceService.initialize();

// Capture current state
const success = await persistenceService.captureState(currentUrl);

// Restore previous state
const restored = await persistenceService.restoreState(targetUrl);

// Check service status
console.log(persistenceService.getStatus());
```

**Advanced Configuration:**
```typescript
// The service automatically loads configuration from environment variables
// but you can check the current configuration:

const service = new WebContainerPersistenceService();
await service.initialize();

// Get detailed status information
const status = service.getStatus();
console.log(status);
// Output: "Persistence enabled: 30 days retention, 5MB max size, 4 exclusion patterns"
```

#### State Structure

The service stores state in a structured format:

```typescript
interface WebContainerState {
  version: string;                    // Service version for compatibility
  timestamp: number;                  // When state was captured
  url: string;                       // Associated URL
  localStorage: Record<string, string>; // Web localStorage data
  cookies: Record<string, string>;    // Web cookies data
  metadata: {
    userAgent: string;               // Browser user agent
    domain: string;                  // Web application domain
    appVersion: string;              // App version
  };
}
```

#### Lifecycle Integration

The service integrates with app lifecycle events:

```typescript
// Automatic capture on app background
App.addListener('appStateChange', async ({ isActive }) => {
  if (!isActive) {
    await persistenceService.captureState(currentUrl);
  }
});

// Manual capture at strategic points
await persistenceService.captureState(currentUrl);
```

#### Security Considerations

- **Pattern Exclusion**: Sensitive data is excluded based on regex patterns
- **Data Validation**: All stored data is validated before restoration
- **Size Limits**: Prevents excessive storage usage
- **Age Limits**: Automatic cleanup of old state data
- **Corruption Handling**: Graceful handling of corrupted state data

#### Troubleshooting

**Common Issues:**

1. **State Not Persisting**
   - Check `VITE_PERSISTENCE_ENABLED=true`
   - Verify sufficient storage space
   - Check console for error messages

2. **State Not Restoring**
   - Ensure URL matches captured state
   - Check state age against retention settings
   - Verify state data integrity

3. **Performance Issues**
   - Reduce `VITE_PERSISTENCE_MAX_SIZE_MB`
   - Increase exclusion patterns for large data
   - Check capture frequency (debounced to 5 seconds)

**Debug Information:**
```typescript
// Get operation status
const inProgress = persistenceService.getOperationStatus();

// Get detailed configuration
const status = persistenceService.getStatus();
console.log(status);
```

### Debug Toolbar Service

The `DebugToolbarService` provides comprehensive debugging capabilities for web container applications, offering real-time state inspection and interactive debugging tools.

#### Key Features

- **Real-time State Inspection**: Live viewing of localStorage and cookie data
- **Interactive Controls**: Buttons for state capture, restoration, and data clearing
- **Draggable Interface**: Repositionable toolbar for optimal development workflow
- **Network Status Display**: Visual indicators for connectivity status
- **PostMessage Communication**: Bidirectional communication with web container
- **Environment-based Activation**: Configurable via environment variables

#### Configuration

The debug toolbar is controlled through environment variables:

```env
# Enable debug toolbar (typically for development only)
VITE_ENABLE_DEBUG_TOOLBAR=true
```

#### Usage Examples

**Basic Integration:**
```typescript
import { DebugToolbarService } from '@/services/DebugToolbarService';

const debugService = new DebugToolbarService();

// Check if debug toolbar is enabled
if (debugService.isEnabled()) {
  // Inject toolbar into web container
  const injected = await debugService.injectToolbar();

  if (injected) {
    console.log('Debug toolbar injected successfully');
  }
}
```

**Manual State Operations:**
```typescript
// Trigger localStorage logging
await debugService.logLocalStorage();

// Clear all cookies
await debugService.clearCookies();

// Remove toolbar
await debugService.removeToolbar();
```

#### Toolbar Interface

When injected, the debug toolbar provides:

**Visual Elements:**
- **Position**: Bottom-right corner (draggable)
- **Collapse/Expand**: Minimizable interface
- **Status Indicators**: Network connectivity and persistence status
- **Action Buttons**: State capture, restore, clear operations

**Interactive Controls:**
```
┌─────────────────────────────┐
│     🔧 Debug Toolbar        │
├─────────────────────────────┤
│ [Log Cookies]               │
│ [Log LocalStorage]          │
│ [Clear Cookies]             │
│ [Clear localStorage]        │
│ [Refresh Page]              │
└─────────────────────────────┘
```

#### Communication Protocol

The toolbar uses PostMessage for communication:

**Message Types:**
- `logLocalStorage`: Request localStorage dump
- `logCookies`: Request cookie information
- `clearCookies`: Clear all cookies
- `clearLocalStorage`: Clear localStorage
- `captureState`: Trigger state capture
- `restoreState`: Trigger state restoration

**Example Message:**
```typescript
// Sent from native app to web container
{
  requestId: "localStorage_1234567890_abc123",
  type: "logLocalStorage"
}

// Response from web container
{
  requestId: "localStorage_1234567890_abc123",
  type: "localStorageData",
  data: {
    key1: "value1",
    key2: "value2"
  }
}
```

#### Development Workflow

**Typical Debug Session:**
1. Enable debug toolbar in environment
2. Start development server
3. Open app in simulator/device
4. Use toolbar to inspect state
5. Test state persistence across app sessions
6. Monitor network connectivity changes

**Best Practices:**
- Only enable in development/testing environments
- Use toolbar to verify state persistence behavior
- Monitor console output for detailed logging
- Test state restoration after app backgrounding

#### Toolbar Positioning

The toolbar is draggable and remembers its position:

```typescript
interface DebugToolbarState {
  position: { x: number; y: number };
  visible: boolean;
  collapsed: boolean;
  lastInjectionTime: number;
}
```

#### Security Considerations

- **Environment Gating**: Only enabled via environment variable
- **Development Only**: Should never be enabled in production
- **Data Exposure**: Displays sensitive application data
- **PostMessage Security**: Uses request IDs to prevent message spoofing

#### Troubleshooting

**Common Issues:**

1. **Toolbar Not Appearing**
   - Check `VITE_ENABLE_DEBUG_TOOLBAR=true`
   - Verify web container is loaded
   - Check console for injection errors

2. **Controls Not Working**
   - Ensure PostMessage communication is established
   - Check for JavaScript errors in web container
   - Verify toolbar injection completed successfully

3. **Performance Impact**
   - Toolbar adds minimal overhead when enabled
   - Disable in production builds
   - Monitor console output for excessive logging

**Debug Information:**
```typescript
// Check if toolbar is enabled
const enabled = debugService.isEnabled();

// Verify injection status
const injected = await debugService.injectToolbar();
console.log('Toolbar injected:', injected);
```

## Project Structure

### Directory Overview

```
SVA Web2App/
├── src/                          # Source code
│   ├── components/               # Reusable Vue components
│   ├── views/                    # Page-level components
│   │   └── WebContainerPage.vue  # Main web container view
│   ├── services/                 # Business logic services
│   │   ├── WebContainerPersistenceService.ts
│   │   └── DebugToolbarService.ts
│   ├── router/                   # Vue Router configuration
│   │   └── index.ts              # Route definitions
│   ├── theme/                    # Styling and theme variables
│   │   └── variables.css         # Ionic theme customization
│   ├── App.vue                   # Root Vue component
│   ├── main.ts                   # Application entry point
│   └── vite-env.d.ts            # TypeScript environment definitions
├── public/                       # Static assets
│   └── favicon.png              # App icon
├── ios/                         # iOS native project
│   └── App/                     # Xcode project files
├── android/                     # Android native project
│   └── app/                     # Android Studio project files
├── tests/                       # Test files
│   ├── unit/                    # Unit tests
│   └── e2e/                     # End-to-end tests
├── dist/                        # Built web assets
├── node_modules/                # Dependencies
├── capacitor.config.ts          # Capacitor configuration
├── ionic.config.json            # Ionic configuration
├── package.json                 # Project dependencies and scripts
├── tsconfig.json                # TypeScript configuration
├── vite.config.ts               # Vite build configuration
├── cypress.config.ts            # Cypress E2E test configuration
└── README.md                    # Project documentation
```

#### Source Code Structure

**`src/main.ts`**
- Application entry point
- Ionic Vue initialization
- CSS imports for Ionic components and theming
- Router integration

**`src/App.vue`**
- Root Vue component
- Basic Ionic app structure with router outlet

**`src/router/index.ts`**
- Vue Router configuration
- Route definitions (currently single route to WebContainerPage)

**`src/views/WebContainerPage.vue`**
- Main application view containing the web container
- Integration with persistence and debug services
- Network monitoring and error handling
- App lifecycle management

#### Service Architecture

**`src/services/WebContainerPersistenceService.ts`**
- State persistence management
- localStorage and cookie capture/restoration
- Data validation and cleanup
- Environment-based configuration

**`src/services/DebugToolbarService.ts`**
- Development debugging capabilities
- PostMessage communication with web container
- Interactive toolbar injection and management
- State inspection and manipulation tools
