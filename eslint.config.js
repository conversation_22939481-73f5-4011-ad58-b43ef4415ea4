/**
 * ESLint Configuration for Ionic Framework + Capacitor + Vue.js Project
 *
 * This configuration is optimized for:
 * - Ionic Vue components and patterns
 * - Capacitor native plugin integration
 * - JavaScript and Vue development
 * - Mobile application development best practices
 *
 * IMPORTANT: This configuration currently supports JavaScript and Vue files only.
 * For full TypeScript support, install these packages:
 * npm install --save-dev @typescript-eslint/parser @typescript-eslint/eslint-plugin
 * npm install --save-dev eslint-plugin-unicorn eslint-plugin-vuejs-accessibility
 * npm install --save-dev eslint-config-prettier eslint-plugin-prettier
 * npm install --save-dev eslint-plugin-cypress
 *
 * Then update the configuration to include TypeScript file patterns and parser settings.
 */

import pluginVue from 'eslint-plugin-vue';

export default [
  {
    name: 'ionic-capacitor/files-to-lint',
    files: [
      '**/*.{js,mjs,cjs,vue}',
      'src/**/*.js',
      'src/**/*.vue',
      'tests/**/*.{js,vue}',
    ],
  },

  {
    name: 'ionic-capacitor/files-to-ignore',
    ignores: [
      // Build outputs
      '**/dist/**',
      '**/dist-ssr/**',
      '**/coverage/**',

      // Capacitor native platforms
      '**/android/**',
      '**/ios/**',

      // Capacitor generated files
      '**/capacitor-cordova-android-plugins/**',
      '**/capacitor-cordova-ios-plugins/**',

      // Node modules and dependencies
      '**/node_modules/**',

      // IDE and system files
      '**/.DS_Store',
      '**/.vscode/**',
      '**/.idea/**',

      // Environment files (may contain sensitive data)
      '**/.env.local',
      '**/.env.*.local',

      // TypeScript files (until TypeScript parser is installed)
      '**/*.ts',
      '**/*.tsx',
      '**/*.d.ts',
      'capacitor.config.ts',
      'cypress.config.ts',
      'vite.config.ts',
      'tsconfig.json',
      'tsconfig.node.json',

      // Vue files with TypeScript (until proper parser configuration)
      'src/views/WebContainerPage.vue', // Contains TypeScript syntax
    ],
  },
  // Vue.js configuration - compatible with Ionic Vue components
  ...pluginVue.configs['flat/essential'], // Using essential instead of strongly-recommended to avoid TypeScript parsing issues
  // Custom rules optimized for Ionic Framework + Capacitor development
  {
    rules: {
      // Code formatting and style
      indent: ['error', 2],

      // Import/module rules - relaxed for Capacitor plugins and native modules
      'import/no-unresolved': 'off', // Capacitor plugins may not resolve in dev environment
      'import/no-extraneous-dependencies': 'off', // Capacitor plugins appear as extraneous
      'import/extensions': 'off', // TypeScript and Vue file extensions are handled automatically

      // Naming conventions - mobile development friendly
      'unicorn/filename-case': 'off', // Ionic components use PascalCase, native files vary
      'unicorn/prevent-abbreviations': 'off', // Mobile development commonly uses abbreviations (e.g., 'btn', 'nav')

      // Code patterns - mobile development friendly
      'unicorn/no-array-for-each': 'off', // forEach is commonly used in mobile event handling

      // Vue.js specific rules - compatible with Ionic Vue components
      'vue/no-mutating-props': ['error', { shallowOnly: true }], // Allow shallow prop mutations for Ionic components
      'vue/multi-word-component-names': 'off', // Ionic components often use single words (e.g., 'Home', 'Settings')

      // Variable usage - development friendly
      'no-unused-vars': [
        'warn',
        {
          argsIgnorePattern: '^_', // Allow unused args prefixed with underscore
          varsIgnorePattern: '^_', // Allow unused vars prefixed with underscore
        },
      ],

      // Console usage - allow in development, warn in production
      'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
      'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',

      // Mobile development specific rules
      'prefer-const': 'error', // Encourage immutability for better performance
      'no-var': 'error', // Use let/const instead of var for block scoping

      // Async/await patterns common in Capacitor plugin usage
      'require-await': 'warn', // Warn about async functions that don't use await
      'no-return-await': 'off', // Allow return await for better stack traces in mobile debugging

      // Vue specific rules for Ionic components
      'vue/no-deprecated-slot-attribute': 'off', // Some Ionic components may use older slot syntax
      'vue/require-default-prop': 'off', // Ionic components often have optional props without defaults

      // Additional rules that can be enabled with TypeScript plugin:
      // '@typescript-eslint/no-unused-vars': ['warn', { argsIgnorePattern: '^_', varsIgnorePattern: '^_' }],
      // '@typescript-eslint/no-explicit-any': 'warn',
      // '@typescript-eslint/no-non-null-assertion': 'warn',
    },
  },
];
