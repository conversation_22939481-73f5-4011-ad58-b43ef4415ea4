/// <reference types="vitest" />

import legacy from '@vitejs/plugin-legacy'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import { defineConfig, loadEnv } from 'vite'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')

  return {
    plugins: [
      vue(),
      legacy()
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    test: {
      globals: true,
      environment: 'jsdom'
    },
    define: {
      // Make environment variables available at build time
      __APP_NAME__: JSON.stringify(env.VITE_APP_NAME || 'Web2App'),
      __TARGET_URL__: JSON.stringify(env.VITE_TARGET_URL || 'https://example.com')
    }
  }
})
